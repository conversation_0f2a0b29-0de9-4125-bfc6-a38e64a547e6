package com.travel.agent.integration;

import com.travel.agent.service.LLMCallService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


import static org.junit.jupiter.api.Assertions.*;

/**
 * LLMCallService 集成测试
 * 直接在Spring环境中测试LLM调用功能
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@SpringBootTest
class LLMCallServiceIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(LLMCallServiceIntegrationTest.class);

    @Autowired
    private LLMCallService llmCallService;



    @Test
    void testCallQwenModel_BasicCall() {
        // 测试基本的LLM调用
        try {
            String systemPrompt = "你是一个有用的助手。";
            String userMessage = "请简单介绍一下北京。";
            
            String response = llmCallService.callQwenModel(systemPrompt, userMessage);
            
            log.info("LLM响应: {}", response);
            assertNotNull(response);
            assertFalse(response.trim().isEmpty());
            
        } catch (Exception e) {
            log.warn("LLM调用失败，可能是API配置问题: {}", e.getMessage());
            // 在集成测试中，如果API未配置，我们记录警告但不让测试失败
        }
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "QWEN_API_KEY", matches = ".*")
    void testCallQwenModel_WithModel() {
        // 测试指定模型的LLM调用
        try {
            String systemPrompt = "你是一个旅游助手。";
            String userMessage = "推荐一个北京的景点。";
            String model = "qwen-turbo";
            
            String response = llmCallService.callQwenModel(systemPrompt, userMessage, model);
            
            log.info("指定模型LLM响应: {}", response);
            assertNotNull(response);
            assertFalse(response.trim().isEmpty());
            
        } catch (Exception e) {
            log.warn("指定模型LLM调用失败: {}", e.getMessage());
        }
    }



    @Test
    @EnabledIfEnvironmentVariable(named = "QWEN_API_KEY", matches = ".*")
    void testTestQwenConnection() {
        // 测试连接检查
        try {
            llmCallService.testQwenConnection();
        } catch (Exception e) {
            log.warn("LLM连接测试失败: {}", e.getMessage());
        }
    }

    @Test
    void testQwenMessageConstructors() {
        // 测试QwenMessage构造函数
        LLMCallService.QwenMessage message1 = new LLMCallService.QwenMessage("user", "Hello");
        assertNotNull(message1);
        assertEquals("user", message1.getRole());
        assertEquals("Hello", message1.getContent());
        
        LLMCallService.QwenMessage message2 = new LLMCallService.QwenMessage("assistant", "Hi there");
        assertNotNull(message2);
        assertEquals("assistant", message2.getRole());
        assertEquals("Hi there", message2.getContent());
    }



    @Test
    @EnabledIfEnvironmentVariable(named = "QWEN_API_KEY", matches = ".*")
    void testLongConversation() {
        // 测试长对话
        try {
            String systemPrompt = "你是一个旅游助手，请简洁回答。";
            String userMessage = "我想了解北京的历史，请简单介绍一下。然后推荐一个历史景点。";
            
            String response = llmCallService.callQwenModel(systemPrompt, userMessage);
            
            log.info("长对话LLM响应长度: {}", response != null ? response.length() : 0);
            assertNotNull(response);
            
        } catch (Exception e) {
            log.warn("长对话测试失败: {}", e.getMessage());
        }
    }

    @Test
    void testErrorHandling() {
        // 测试错误处理
        try {
            // 使用无效的系统提示词测试错误处理
            String response = llmCallService.callQwenModel(null, "test message");
            log.info("错误处理测试响应: {}", response);
            
        } catch (Exception e) {
            log.info("预期的错误处理: {}", e.getMessage());
            // 这是预期的行为
        }
    }


}