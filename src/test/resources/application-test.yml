server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: travel-agent

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: agent
    password: Qaz#123!

# MyBatis Plus配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 实体类扫描包
  type-aliases-package: com.travel.agent.entity
  # Mapper XML文件位置
  mapper-locations: classpath:mapper/*.xml
  # 全局配置
  global-config:
    db-config:
      # 主键策略
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0

# 千问API配置
dashscope:
  api:
    # API密钥，请在环境变量中设置DASHSCOPE_API_KEY或在此处配置
    key: sk-9e29becac60944dd833ac2d1b753dbe4
    # API地址
    url: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions

# JWT配置
jwt:
  secret: travel-agent-secret-key-for-jwt-token-generation-must-be-at-least-256-bits
  expiration: 86400000 # 24小时，单位毫秒

# 日志配置
logging:
  level:
    com.travel.agent: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"