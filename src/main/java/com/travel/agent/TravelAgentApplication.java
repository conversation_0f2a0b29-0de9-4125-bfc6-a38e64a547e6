package com.travel.agent;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;

/**
 * Travel Agent Application 主启动类
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@SpringBootApplication
@MapperScan("com.travel.agent.mapper")
@EnableRetry
public class TravelAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(TravelAgentApplication.class, args);
    }
}