package com.travel.agent.service;

import com.travel.agent.entity.Node;
import com.travel.agent.entity.NodeContext;
import com.travel.agent.entity.SystemPrompt;
import com.travel.agent.enums.ContextKeyEnum;
import com.travel.agent.mapper.NodeContextMapper;
import com.travel.agent.mapper.NodeMapper;
import com.travel.agent.mapper.SystemPromptMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Service
public class ContextService {

    private static final Logger log = LoggerFactory.getLogger(ContextService.class);

    @Resource
    private NodeContextMapper nodeContextMapper;
    @Resource
    private SystemPromptMapper systemPromptMapper;
    @Resource
    private NodeMapper nodeMapper;




    public Map<ContextKeyEnum, String> getRunNodeContext(Node node) {
        List<NodeContext> byNodeId = nodeContextMapper.findByNodeId(node.getId());

        SystemPrompt byAgentName = systemPromptMapper.findByAgentName(node.getAgentName());


        // 组装LLM 调用消息
        String systemPrompt = byAgentName.getPrompt();

        // 找出用户消息
        Optional<NodeContext> first = byNodeId.stream()
                .filter(context -> context.getContextKey().equals(ContextKeyEnum.USER_MESSAGE.getCode()))
                .findFirst();
        if(first.isEmpty()){
            throw new RuntimeException("没有找到用户消息");
        }
        // 处理用户消息中的占位符替换
        String userMessage = first.get().getContextValue();
        
        // planAgent 使用原始用户输入，不需要替换占位符
        if (!"plan_agent".equals(node.getAgentName())) {
            // 其他 agent 的 userMessage 为 planAgent 规划出来的，如果存在占位符 {} 说明依赖前序节点的 result
            userMessage = replacePlaceholders(userMessage, node);
        }

        Map<ContextKeyEnum, String> contextMap = new HashMap<>();
        contextMap.put(ContextKeyEnum.SYSTEM_PROMPT, systemPrompt);
        contextMap.put(ContextKeyEnum.USER_MESSAGE, userMessage);
        return contextMap;
    }



    public void saveNodeContext(Node node, ContextKeyEnum contextKey, String contextValue) {
        NodeContext nodeContext = new NodeContext();
        nodeContext.setNodeId(node.getId());
        nodeContext.setContextKey(contextKey.getCode());
        nodeContext.setContextValue(contextValue);
        nodeContextMapper.insert(nodeContext);
    }

    /**
     * 替换用户消息中的占位符 {}
     * 按照链表前序关系从前序节点的 result 中获取数据进行替换
     * 
     * @param userMessage 包含占位符的用户消息
     * @param currentNode 当前节点
     * @return 替换占位符后的用户消息
     */
    private String replacePlaceholders(String userMessage, Node currentNode) {
        if (userMessage == null || !userMessage.contains("{}")) {
            return userMessage;
        }
        
        try {
            log.debug("开始替换占位符，当前节点: {}, 原始消息: {}", currentNode.getId(), userMessage);
            
            // 收集链表中的前序节点结果，按照从最早到最近的顺序
            List<String> predecessorResults = collectPredecessorResults(currentNode);
            
            // 按顺序替换占位符 {}
            String result = userMessage;
            int placeholderIndex = 0;
            
            while (result.contains("{}") && placeholderIndex < predecessorResults.size()) {
                String replacement = predecessorResults.get(placeholderIndex);
                result = result.replaceFirst("\\{\\}", replacement);
                log.debug("替换第 {} 个占位符: {}", placeholderIndex + 1, replacement.substring(0, Math.min(50, replacement.length())) + "...");
                placeholderIndex++;
            }
            
            log.debug("占位符替换完成，结果消息长度: {}", result.length());
            return result;
            
        } catch (Exception e) {
            log.error("替换占位符失败，返回原始消息", e);
            return userMessage;
        }
    }
    
    /**
     * 收集前序节点的结果，按照从最早到最近的顺序
     * 
     * @param currentNode 当前节点
     * @return 前序节点结果列表，按执行顺序排列
     */
    private List<String> collectPredecessorResults(Node currentNode) {
        List<String> results = new ArrayList<>();
        
        // 首先收集所有前序节点（从当前节点向前遍历）
        List<Node> predecessorNodes = new ArrayList<>();
        Node currentPrevNode = currentNode;
        
        while (currentPrevNode.getPrevNodeId() != null) {
            Node prevNode = nodeMapper.selectById(currentPrevNode.getPrevNodeId());
            if (prevNode == null) {
                log.warn("找不到前序节点: {}", currentPrevNode.getPrevNodeId());
                break;
            }
            
            // 只收集已完成的节点
            if (Node.NodeStatus.COMPLETED.equals(prevNode.getStatus())) {
                predecessorNodes.add(prevNode);
            }
            
            currentPrevNode = prevNode;
        }
        
        // 反转列表，使其按照从最早到最近的顺序
        for (int i = predecessorNodes.size() - 1; i >= 0; i--) {
            Node node = predecessorNodes.get(i);
            
            // 获取节点的结果
            List<NodeContext> nodeContexts = nodeContextMapper.findByNodeId(node.getId());
            for (NodeContext context : nodeContexts) {
                if (ContextKeyEnum.RESULT.getCode().equals(context.getContextKey())) {
                    results.add(context.getContextValue());
                    log.debug("收集到前序节点 {} 的结果用于占位符替换", node.getAgentName());
                    break;
                }
            }
        }
        
        return results;
    }




















}
