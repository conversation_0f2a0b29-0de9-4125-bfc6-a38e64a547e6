package com.travel.agent.service;

import com.travel.agent.dto.AuthResponse;
import com.travel.agent.dto.LoginRequest;
import com.travel.agent.dto.RegisterRequest;
import com.travel.agent.entity.User;
import com.travel.agent.mapper.UserMapper;
import com.travel.agent.util.JwtUtil;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 认证服务类
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Service
public class AuthService {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    public AuthService(UserMapper userMapper, PasswordEncoder passwordEncoder, JwtUtil jwtUtil) {
        this.userMapper = userMapper;
        this.passwordEncoder = passwordEncoder;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 用户注册
     * 
     * @param registerRequest 注册请求
     * @return 认证响应
     */
    public AuthResponse register(RegisterRequest registerRequest) {
        // 检查邮箱是否已存在
        User existingUser = userMapper.findByEmail(registerRequest.getEmail());
        if (existingUser != null) {
            throw new RuntimeException("邮箱已被注册");
        }

        // 检查密码确认
        if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        // 创建新用户
        User user = new User();
        user.setEmail(registerRequest.getEmail());
        user.setPasswordHash(passwordEncoder.encode(registerRequest.getPassword()));
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        // 保存用户
        userMapper.insert(user);

        // 生成JWT token
        String token = jwtUtil.generateToken(user.getId(), user.getEmail());

        return new AuthResponse(token, user.getId(), user.getEmail());
    }

    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @return 认证响应
     */
    public AuthResponse login(LoginRequest loginRequest) {
        // 查找用户
        User user = userMapper.findByEmail(loginRequest.getEmail());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证密码
        if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPasswordHash())) {
            throw new RuntimeException("密码错误");
        }

        // 生成JWT token
        String token = jwtUtil.generateToken(user.getId(), user.getEmail());

        return new AuthResponse(token, user.getId(), user.getEmail());
    }

    /**
     * 根据邮箱获取用户信息
     * 
     * @param email 用户邮箱
     * @return 用户信息
     */
    public User getUserByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    /**
     * 验证token并获取用户信息
     * 
     * @param token JWT token
     * @return 用户信息
     */
    public User validateTokenAndGetUser(String token) {
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("无效的token");
        }

        String email = jwtUtil.getEmailFromToken(token);
        User user = userMapper.findByEmail(email);
        
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return user;
    }
}