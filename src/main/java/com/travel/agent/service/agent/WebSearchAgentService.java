package com.travel.agent.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.travel.agent.entity.Node;
import com.travel.agent.entity.NodeContext;
import com.travel.agent.enums.ContextKeyEnum;
import com.travel.agent.mapper.NodeContextMapper;
import com.travel.agent.mapper.NodeMapper;
import com.travel.agent.service.ContextService;
import com.travel.agent.service.LLMCallService;
import com.travel.agent.service.tool.FunctionCallTool;
import com.travel.agent.service.tool.WebSearchTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class WebSearchAgentService {

    private static final Logger log = LoggerFactory.getLogger(WebSearchAgentService.class);

    @Autowired
    private NodeMapper nodeMapper;

    @Autowired
    private NodeContextMapper nodeContextMapper;

    @Autowired
    private LLMCallService llmCallService;

    @Autowired
    private ContextService contextService;

    @Autowired
    private WebSearchTool webSearchTool;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     提示词

     你是一个智能搜索助手，使用ReAct（Reasoning and Acting）模式来处理用户的搜索任务。

     ## ReAct工作流程：
     1. **Thought（思考）**: 分析当前情况，思考下一步应该做什么
     2. **Action（行动）**: 选择并执行合适的工具
     3. **Observation（观察）**: 观察工具执行的结果
     4. **重复循环**: 根据观察结果继续思考，直到能给出最终答案

     ## 可用工具：
     - `search_web`: 在网络上搜索一般信息
     - `search_news`: 搜索新闻信息
     - `extract_content_from_url`: 从指定URL提取内容
     - `think`: 思考和分析工具（用于深度思考和推理）

     ## 重要规则：
     1. 每次调用搜索工具后，系统会自动调用think工具进行分析
     2. 在每轮循环中，先进行思考（Thought），然后决定行动（Action）
     3. 观察工具结果后，继续思考是否需要更多信息或可以给出最终答案
     4. 当你认为已经收集到足够信息时，请明确说明"最终答案"或"Final Answer"
     5. 保持思考过程清晰，让用户能够跟踪你的推理过程

     请按照ReAct模式处理用户任务，展示完整的思考-行动-观察循环。
     */

    /**
     * 使用标准ReAct模式处理搜索任务
     */
    public void run(Node node) {
        try {
            log.info("开始执行 WebSearchAgent 节点: {}", node.getId());
            
            // 1. 更新节点状态为运行中
            node.setStatus(Node.NodeStatus.RUNNING);
            nodeMapper.updateById(node);
            
            // 2. 获取节点上下文
            Map<ContextKeyEnum, String> context = contextService.getRunNodeContext(node);
            String systemPrompt = context.get(ContextKeyEnum.SYSTEM_PROMPT);
            contextService.saveNodeContext(node, ContextKeyEnum.SYSTEM_PROMPT, systemPrompt);
            
            // 4. 调用 react 方法处理搜索任务
            String result = react(systemPrompt, context.get(ContextKeyEnum.USER_MESSAGE));
            
            // 5. 保存结果到上下文
            NodeContext resultContext = new NodeContext();
            resultContext.setNodeId(node.getId());
            resultContext.setContextKey("search_result");
            resultContext.setContextValue(result);
            nodeContextMapper.insert(resultContext);
            
            // 6. 更新节点状态为完成
            node.setStatus(Node.NodeStatus.COMPLETED);
            nodeMapper.updateById(node);
            
            log.info("WebSearchAgent 节点执行完成: {}", node.getId());
            
        } catch (Exception e) {
            log.error("WebSearchAgent 节点执行失败: {}", node.getId(), e);
            
            // 更新节点状态为失败
            node.setStatus(Node.NodeStatus.FAILED);
            nodeMapper.updateById(node);
            
            // 保存错误信息
            NodeContext errorContext = new NodeContext();
            errorContext.setNodeId(node.getId());
            errorContext.setContextKey("error");
            errorContext.setContextValue("执行失败: " + e.getMessage());
            nodeContextMapper.insert(errorContext);
        }
    }

    /**
     * 使用ReAct模式进行搜索和推理
     */
    private String react(String systemPrompt, String userMessage) {
        try {
            log.info("开始 WebSearch ReAct 循环，用户消息: {}", userMessage);
            
            // 1. 获取可用工具
            List<FunctionCallTool> tools = getAvailableTools();
            
            // 2. 构建对话历史
            List<LLMCallService.QwenMessage> conversationHistory = new ArrayList<>();
            
            // 3. ReAct 循环
            int maxIterations = 5;
            for (int iteration = 1; iteration <= maxIterations; iteration++) {
                log.info("开始第 {} 轮 ReAct 循环", iteration);
                
                // 构建当前轮次的用户消息
                String currentUserMessage = iteration == 1 ? userMessage : 
                    "请继续分析以上信息，如果需要更多信息请使用工具，否则给出最终答案。";
                
                // 调用 LLM
                LLMCallService.QwenResponseWithTools response = llmCallService.callQwenModelWithTools(
                    systemPrompt, buildConversationString(conversationHistory, currentUserMessage), tools);
                
                if (response == null || response.getChoices() == null || response.getChoices().isEmpty()) {
                    log.error("LLM 响应为空或无效");
                    return "抱歉，AI 服务暂时不可用，请稍后再试。";
                }
                
                LLMCallService.QwenChoiceWithTools choice = response.getChoices().get(0);
                LLMCallService.QwenMessageWithTools message = choice.getMessage();
                
                // 解析结果
                String content = message.getContent();
                List<LLMCallService.ToolCall> toolCalls = message.getToolCalls();
                
                log.debug("第 {} 轮 LLM 响应内容: {}", iteration, content);
                log.debug("第 {} 轮工具调用数量: {}", iteration, toolCalls != null ? toolCalls.size() : 0);
                
                // 判断是否需要工具调用
                if (toolCalls != null && !toolCalls.isEmpty()) {
                    log.info("第 {} 轮需要执行 {} 个工具调用", iteration, toolCalls.size());
                    
                    // 添加助手的响应到对话历史
                    LLMCallService.QwenMessageWithTools assistantMessage = new LLMCallService.QwenMessageWithTools(
                        "assistant", content, toolCalls);
                    conversationHistory.add(assistantMessage);
                    
                    // 执行工具调用
                    boolean hasToolErrors = false;
                    for (LLMCallService.ToolCall toolCall : toolCalls) {
                        String toolName = toolCall.getFunction().getName();
                        String argumentsJson = toolCall.getFunction().getArguments();
                        
                        log.info("第 {} 轮执行工具调用: {}, 参数: {}", iteration, toolName, argumentsJson);
                        
                        // 解析参数
                        Map<String, Object> parameters;
                        try {
                            parameters = objectMapper.readValue(argumentsJson, new TypeReference<Map<String, Object>>() {});
                        } catch (JsonProcessingException e) {
                            log.error("解析工具参数失败: {}", argumentsJson, e);
                            parameters = Map.of();
                        }
                        
                        // 调用工具
                        WebSearchTool.ToolCallResult result = webSearchTool.callTool(toolName, parameters);
                        
                        // 构建工具结果消息
                        String toolResultContent;
                        if (result.isSuccess()) {
                            try {
                                toolResultContent = objectMapper.writeValueAsString(result.getData());
                            } catch (JsonProcessingException e) {
                                toolResultContent = result.getData().toString();
                            }
                            log.info("第 {} 轮工具调用成功: {}, 结果长度: {}", iteration, toolName, toolResultContent.length());
                        } else {
                            toolResultContent = "工具调用失败: " + result.getError();
                            log.warn("第 {} 轮工具调用失败: {}, 错误: {}", iteration, toolName, result.getError());
                            hasToolErrors = true;
                        }
                        
                        // 添加工具结果到对话历史
                        LLMCallService.QwenMessageWithTools toolResultMessage = new LLMCallService.QwenMessageWithTools(
                            "tool", toolResultContent);
                        toolResultMessage.setToolCallId(toolCall.getId());
                        conversationHistory.add(toolResultMessage);
                    }
                    
                    // 如果是最后一轮或者工具调用有错误，直接返回当前结果
                    if (iteration == maxIterations || hasToolErrors) {
                        log.info("React 循环结束（第 {} 轮），进行最终 LLM 调用", iteration);
                        return getFinalResponse(systemPrompt, conversationHistory, tools);
                    }
                    
                    // 继续下一轮循环
                    log.info("第 {} 轮完成，继续下一轮 React 循环", iteration);
                    
                } else {
                    // 如果不需要工具调用，则返回结果
                    log.info("第 {} 轮无需工具调用，返回最终结果", iteration);
                    return content != null ? content : "抱歉，无法生成有效回答。";
                }
            }
            
            // 如果循环结束仍未得到最终答案，进行最终调用
            log.info("React 循环达到最大轮次，进行最终 LLM 调用");
            return getFinalResponse(systemPrompt, conversationHistory, tools);
            
        } catch (Exception e) {
            log.error("WebSearch ReAct 处理失败", e);
            return "抱歉，处理您的搜索请求时发生错误，请稍后再试。";
        }
    }

    /**
     * 获取最终响应
     */
    private String getFinalResponse(String systemPrompt, List<LLMCallService.QwenMessage> conversationHistory, 
                                   List<FunctionCallTool> tools) {
        try {
            // 添加最终指令，要求 AI 基于已有信息给出最终答案
            String finalInstruction = "请基于以上的搜索结果和分析，为用户提供完整、准确的最终答案。不要再调用工具。";
            conversationHistory.add(new LLMCallService.QwenMessage("user", finalInstruction));
            
            // 进行最终的 LLM 调用
            LLMCallService.QwenResponseWithTools finalResponse = llmCallService.callQwenModelWithTools(
                systemPrompt, buildConversationString(conversationHistory), tools);
            
            if (finalResponse != null && finalResponse.getChoices() != null && !finalResponse.getChoices().isEmpty()) {
                LLMCallService.QwenChoiceWithTools choice = finalResponse.getChoices().get(0);
                String content = choice.getMessage().getContent();
                log.info("获取到最终响应，内容长度: {}", content != null ? content.length() : 0);
                return content != null ? content : "抱歉，无法生成最终回答。";
            } else {
                log.error("最终 LLM 调用响应为空");
                return "抱歉，AI 服务暂时不可用，请稍后再试。";
            }
        } catch (Exception e) {
            log.error("获取最终响应失败", e);
            return "抱歉，处理您的请求时发生错误，请稍后再试。";
        }
    }

    /**
     * 构建对话字符串
     */
    private String buildConversationString(List<LLMCallService.QwenMessage> conversationHistory) {
        if (conversationHistory.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (LLMCallService.QwenMessage message : conversationHistory) {
            sb.append(message.getRole()).append(": ").append(message.getContent()).append("\n\n");
        }
        return sb.toString();
    }

    /**
     * 构建对话字符串（包含当前用户消息）
     */
    private String buildConversationString(List<LLMCallService.QwenMessage> conversationHistory, String currentUserMessage) {
        StringBuilder sb = new StringBuilder();
        
        // 添加历史对话
        for (LLMCallService.QwenMessage message : conversationHistory) {
            sb.append(message.getRole()).append(": ").append(message.getContent()).append("\n\n");
        }
        
        // 添加当前用户消息
        if (currentUserMessage != null && !currentUserMessage.trim().isEmpty()) {
            sb.append("user: ").append(currentUserMessage);
        }
        
        return sb.toString();
    }



    /**
     * 获取可用工具列表
     */
    private List<FunctionCallTool> getAvailableTools() {
        List<FunctionCallTool> tools = new ArrayList<>();
        
        // 添加所有可用的搜索工具
        for (String toolName : webSearchTool.getAvailableToolNames()) {
            FunctionCallTool tool = webSearchTool.getFunctionCallTool(toolName);
            if (tool != null) {
                tools.add(tool);
            }
        }
        
        return tools;
    }
}
