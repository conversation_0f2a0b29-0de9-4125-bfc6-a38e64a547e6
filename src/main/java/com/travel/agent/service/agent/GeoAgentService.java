package com.travel.agent.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.travel.agent.entity.Node;
import com.travel.agent.enums.ContextKeyEnum;
import com.travel.agent.mapper.NodeMapper;
import com.travel.agent.service.ContextService;
import com.travel.agent.service.LLMCallService;
import com.travel.agent.service.NodeService;
import com.travel.agent.service.tool.FunctionCallTool;
import com.travel.agent.service.tool.GeoTool;
import com.travel.agent.service.tool.ToolCallResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service
public class GeoAgentService {

    private static final Logger log = LoggerFactory.getLogger(GeoAgentService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private NodeMapper nodeMapper;
    @Resource
    private LLMCallService llmCallService;
    @Resource
    private ContextService contextService;
    @Resource
    private GeoTool geoTool;




    public void run(Node node){
        // 1.获取上下文
        Map<ContextKeyEnum, String> context = contextService.getRunNodeContext(node);
        String systemPrompt = context.get(ContextKeyEnum.SYSTEM_PROMPT);
        contextService.saveNodeContext(node, ContextKeyEnum.SYSTEM_PROMPT, systemPrompt);
        // 4.调用llm
        String response = react(systemPrompt, context.get(ContextKeyEnum.USER_MESSAGE));
        // 更新节点状态
        node.setStatus(Node.NodeStatus.COMPLETED);
        nodeMapper.updateById(node);
        contextService.saveNodeContext(node, ContextKeyEnum.RESULT, response);
    }



    private String react(String systemPrompt, String userMessage){
        try {
            log.info("开始执行 React 模式，用户消息: {}", userMessage);
            
            // 获取可用的工具列表
            List<FunctionCallTool> tools = geoTool.getFunctionCallTools();
            log.debug("获取到 {} 个可用工具", tools.size());
            
            // 构建包含工具信息的系统提示词
            String enhancedSystemPrompt = buildSystemPromptWithTools(systemPrompt, tools);
            
            // 构建对话历史
            List<LLMCallService.QwenMessage> conversationHistory = new ArrayList<>();
            conversationHistory.add(new LLMCallService.QwenMessage("system", enhancedSystemPrompt));
            conversationHistory.add(new LLMCallService.QwenMessage("user", userMessage));
            
            // React 循环，最大5次
            int maxIterations = 5;
            for (int iteration = 1; iteration <= maxIterations; iteration++) {
                log.info("React 循环第 {} 轮开始", iteration);
                
                // 使用 Function Call 调用 LLM
                LLMCallService.QwenResponseWithTools response = llmCallService.callQwenModelWithTools(
                    enhancedSystemPrompt, buildConversationString(conversationHistory), tools);
                
                if (response == null || response.getChoices() == null || response.getChoices().isEmpty()) {
                    log.error("LLM 响应为空或无效");
                    return "抱歉，AI 服务暂时不可用，请稍后再试。";
                }
                
                LLMCallService.QwenChoiceWithTools choice = response.getChoices().get(0);
                LLMCallService.QwenMessageWithTools message = choice.getMessage();
                
                // 1. 解析结果
                String content = message.getContent();
                List<LLMCallService.ToolCall> toolCalls = message.getToolCalls();
                
                log.debug("第 {} 轮 LLM 响应内容: {}", iteration, content);
                log.debug("第 {} 轮工具调用数量: {}", iteration, toolCalls != null ? toolCalls.size() : 0);
                
                // 2. 判断是否需要 tool call
                if (toolCalls != null && !toolCalls.isEmpty()) {
                    // 3. 如果需要 tool call，调用 tool
                    log.info("第 {} 轮需要执行 {} 个工具调用", iteration, toolCalls.size());
                    
                    // 添加助手的响应（包含工具调用）到对话历史
                    LLMCallService.QwenMessageWithTools assistantMessage = new LLMCallService.QwenMessageWithTools(
                        "assistant", content, toolCalls);
                    conversationHistory.add(assistantMessage);
                    
                    // 执行每个工具调用并添加结果到对话历史
                    boolean hasToolErrors = false;
                    for (LLMCallService.ToolCall toolCall : toolCalls) {
                        String toolName = toolCall.getFunction().getName();
                        String argumentsJson = toolCall.getFunction().getArguments();
                        
                        log.info("第 {} 轮执行工具调用: {}, 参数: {}", iteration, toolName, argumentsJson);
                        
                        // 解析参数
                        Map<String, Object> parameters;
                        try {
                            parameters = objectMapper.readValue(argumentsJson, new TypeReference<Map<String, Object>>() {});
                        } catch (JsonProcessingException e) {
                            log.error("解析工具参数失败: {}", argumentsJson, e);
                            parameters = Map.of();
                        }
                        
                        // 调用工具
                        ToolCallResult result = geoTool.callTool(toolName, parameters);
                        
                        // 构建工具结果消息
                        String toolResultContent;
                        if (result.isSuccess()) {
                            try {
                                toolResultContent = objectMapper.writeValueAsString(result.getData());
                            } catch (JsonProcessingException e) {
                                toolResultContent = result.getData().toString();
                            }
                            log.info("第 {} 轮工具调用成功: {}, 结果长度: {}", iteration, toolName, toolResultContent.length());
                        } else {
                            toolResultContent = "工具调用失败: " + result.getError();
                            log.warn("第 {} 轮工具调用失败: {}, 错误: {}", iteration, toolName, result.getError());
                            hasToolErrors = true;
                        }
                        
                        // 添加工具结果到对话历史
                        LLMCallService.QwenMessageWithTools toolResultMessage = new LLMCallService.QwenMessageWithTools(
                            "tool", toolResultContent);
                        toolResultMessage.setToolCallId(toolCall.getId());
                        conversationHistory.add(toolResultMessage);
                    }
                    
                    // 如果是最后一轮或者工具调用有错误，直接返回当前结果
                    if (iteration == maxIterations || hasToolErrors) {
                        log.info("React 循环结束（第 {} 轮），进行最终 LLM 调用", iteration);
                        return getFinalResponse(enhancedSystemPrompt, conversationHistory, tools);
                    }
                    
                    // 继续下一轮循环
                    log.info("第 {} 轮完成，继续下一轮 React 循环", iteration);
                    
                } else {
                    // 4. 如果不需要 tool call，则返回结果
                    log.info("第 {} 轮无需工具调用，React 循环结束", iteration);
                    return content != null ? content : "抱歉，我无法理解您的问题，请重新描述。";
                }
            }
            
            // 如果循环结束仍未得到最终答案，进行最终调用
            log.info("React 循环达到最大次数 {}，进行最终 LLM 调用", maxIterations);
            return getFinalResponse(enhancedSystemPrompt, conversationHistory, tools);
            
        } catch (Exception e) {
            log.error("React 模式执行失败", e);
            return "抱歉，处理您的请求时发生错误，请稍后再试。";
        }
    }
    
    /**
     * 构建包含工具信息的系统提示词
     */
    private String buildSystemPromptWithTools(String baseSystemPrompt, List<FunctionCallTool> tools) {
        StringBuilder toolListBuilder = new StringBuilder();
        for (FunctionCallTool tool : tools) {
            toolListBuilder.append("- ").append(tool.getFunction().getName())
                          .append(": ").append(tool.getFunction().getDescription())
                          .append("\n");
        }
        
        return baseSystemPrompt.replace("{tool_list}", toolListBuilder.toString());
    }
    
    /**
     * 获取最终响应
     */
    private String getFinalResponse(String systemPrompt, List<LLMCallService.QwenMessage> conversationHistory, 
                                   List<FunctionCallTool> tools) {
        try {
            // 添加最终指令，要求 AI 基于已有信息给出最终答案
            String finalInstruction = "请基于以上的工具调用结果，为用户提供完整、准确的最终答案。不要再调用工具。";
            conversationHistory.add(new LLMCallService.QwenMessage("user", finalInstruction));
            
            // 进行最终的 LLM 调用
            LLMCallService.QwenResponseWithTools finalResponse = llmCallService.callQwenModelWithTools(
                systemPrompt, buildConversationString(conversationHistory), tools);
            
            if (finalResponse != null && finalResponse.getChoices() != null && !finalResponse.getChoices().isEmpty()) {
                LLMCallService.QwenChoiceWithTools choice = finalResponse.getChoices().get(0);
                String content = choice.getMessage().getContent();
                log.info("获取到最终响应，内容长度: {}", content != null ? content.length() : 0);
                return content != null ? content : "抱歉，无法生成最终回答。";
            } else {
                log.error("最终 LLM 调用响应为空");
                return "抱歉，AI 服务暂时不可用，请稍后再试。";
            }
        } catch (Exception e) {
            log.error("获取最终响应失败", e);
            return "抱歉，处理您的请求时发生错误，请稍后再试。";
        }
    }
    
    /**
     * 构建对话字符串
     */
    private String buildConversationString(List<LLMCallService.QwenMessage> conversationHistory) {
        if (conversationHistory == null || conversationHistory.isEmpty()) {
            return "";
        }
        
        StringBuilder conversation = new StringBuilder();
        for (LLMCallService.QwenMessage message : conversationHistory) {
            if ("system".equals(message.getRole())) {
                continue; // 系统消息已经在 systemPrompt 中处理
            }
            
            conversation.append(message.getRole()).append(": ").append(message.getContent()).append("\n\n");
        }
        
        return conversation.toString().trim();
    }




















}
