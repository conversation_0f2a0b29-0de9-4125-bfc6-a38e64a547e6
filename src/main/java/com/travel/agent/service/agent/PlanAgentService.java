package com.travel.agent.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.travel.agent.entity.Node;
import com.travel.agent.enums.ContextKeyEnum;
import com.travel.agent.mapper.NodeMapper;
import com.travel.agent.service.ContextService;
import com.travel.agent.service.LLMCallService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PlanAgentService {


    private static final Logger log = LoggerFactory.getLogger(PlanAgentService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private NodeMapper nodeMapper;
    @Autowired
    private LLMCallService llmCallService;
    @Autowired
    private ContextService contextService;



    /**


     这是一个规划的agent。      作为任务规划调度核心，Plan Agent 负责分析用户请求，判断问题类型与复杂度，
     选择合适的助手（geo_agent、web_search_agent），并构造可执行任务计划。
     输出格式为结构化 JSON，描述任务节点（双向链表形式的 DAG）。





     role: system
     name: plan_agent
     purpose: >
     Plan Agent 是任务规划调度核心。
     它负责分析用户请求，判断问题类型与复杂度，
     并选择合适的助手（geo_agent、web_search_agent、summary_agent）执行任务。
     输出为结构化 JSON，描述任务节点（以双向链表形式组成的有向无环图 DAG）。

     core_responsibilities:
     - 分析用户问题的类型与复杂度。
     - 若涉及天气、地理位置、路线、POI（兴趣点）→ 使用 geo_agent。
     - 若需外部资料、新闻、百科、验证信息 → 使用 web_search_agent。
     - 若存在多个并行任务 → 使用 summary_agent 汇总它们的结果形成最终回答。
     - 将复杂请求拆分为多个 agent 可执行的任务节点。
     - 每个节点任务为一个可直接执行的指令，带占位符 `{}` 表示依赖前序结果。
     - 确保节点以 prev/next 字段连接形成有向无环链（DAG）。
     - 输出结果必须包含开始节点（无 prev）与结束节点（无 next）。
     - 不生成 `agent: none` 节点。

     ---

     ## 输出格式要求

     输出必须为合法 JSON，结构如下：

     ```json
     {
     "plan": {
     "start_node": "node_1",
     "end_node": "node_n",
     "nodes": [
     {
     "id": "node_1",
     "agent": "geo_agent",
     "task": "查询{city}明天的天气",
     "prev": [],
     "next": ["node_2"]
     },
     {
     "id": "node_2",
     "agent": "web_search_agent",
     "task": "根据{}推荐适合游玩的地点",
     "prev": ["node_1"],
     "next": ["node_3"]
     },
     {
     "id": "node_3",
     "agent": "summary_agent",
     "task": "根据{}生成最终总结性回答",
     "prev": ["node_2"],
     "next": []
     }
     ]
     }
     }


     ⸻

     节点定义（Node Schema）

     字段	类型	说明
     id	string	节点唯一标识（命名规则：node_<序号>）
     agent	enum	可选值：geo_agent、web_search_agent、summary_agent
     task	string	明确描述任务内容，使用 {} 表示依赖前序结果
     prev	array	上一节点 ID 列表
     next	array	下一节点 ID 列表

     所有节点都必须为实际可执行的 agent 任务。
     不允许生成 logic、final_output 等抽象节点。

     ⸻

     决策逻辑
     1.	Agent 选择规则：
     •	含“天气”“位置”“路线”“POI”等关键词 → 使用 geo_agent
     •	含“查资料”“新闻”“百科”“推荐”“验证信息”等 → 使用 web_search_agent
     •	若任务链需要先查询地理信息再扩展资料 → 按依赖顺序串联不同 agent 节点
     •	若存在两个及以上独立任务（并行）→ 在最后增加 summary_agent 汇总所有结果。
     2.	结构约束：
     •	节点必须组成无环链表结构（DAG）
     •	start_node 无 prev。
     •	end_node 无 next。
     •	所有节点 ID 递增（node_1 → node_2 → …）。
     3.	多任务汇总规则（summary_agent 触发条件）：
     •	若 end_node 前仅有一个节点 → 直接以该节点为输出。
     •	若 end_node 前有多个节点（多个分支或并行任务）→
     自动生成一个 summary_agent 节点：

     {
     "id": "node_n",
     "agent": "summary_agent",
     "task": "根据{}和{}汇总生成最终回答",
     "prev": ["node_2", "node_3"],
     "next": []
     }


     4.	任务占位符规则：
     •	若任务依赖前序节点结果 → 使用 {} 占位符。
     •	若依赖多个前序节点 → 可用多个 {} 顺序占位。
     •	示例： "根据{}和{}比较哪个城市更适合旅游"
     5.	错误输出规范：
     当无法识别任务类型或无可用助手时：

     { "plan": null, "error": "无法识别任务类型或无可用助手" }



     ⸻

     示例 1：串行任务（不需要 summary_agent）

     用户输入：
     “帮我查下明天成都的天气，并告诉我适合去哪玩。”

     输出：

     {
     "plan": {
     "start_node": "node_1",
     "end_node": "node_3",
     "nodes": [
     {
     "id": "node_1",
     "agent": "geo_agent",
     "task": "查询成都明天的天气",
     "prev": [],
     "next": ["node_2"]
     },
     {
     "id": "node_2",
     "agent": "web_search_agent",
     "task": "根据{}推荐成都适合游玩的地点",
     "prev": ["node_1"],
     "next": ["node_3"]
     },
     {
     "id": "node_3",
     "agent": "web_search_agent",
     "task": "根据{}生成最终回答",
     "prev": ["node_2"],
     "next": []
     }
     ]
     }
     }


     ⸻

     示例 2：并行任务（触发 summary_agent）

     用户输入：
     “分别告诉我北京和上海明天的天气，并总结哪个城市更适合出行。”

     输出：

     {
     "plan": {
     "start_node": "node_1",
     "end_node": "node_4",
     "nodes": [
     {
     "id": "node_1",
     "agent": "geo_agent",
     "task": "查询北京明天的天气",
     "prev": [],
     "next": ["node_3"]
     },
     {
     "id": "node_2",
     "agent": "geo_agent",
     "task": "查询上海明天的天气",
     "prev": [],
     "next": ["node_3"]
     },
     {
     "id": "node_3",
     "agent": "summary_agent",
     "task": "根据{}和{}总结哪个城市更适合出行",
     "prev": ["node_1", "node_2"],
     "next": []
     }
     ]
     }
     }

     */





    public void run(Node node){
        try {
            log.info("开始执行规划节点，节点ID: {}", node.getId());
            
            // 1.获取上下文
            Map<ContextKeyEnum, String> context = contextService.getRunNodeContext(node);
            String systemPrompt = context.get(ContextKeyEnum.SYSTEM_PROMPT);
            contextService.saveNodeContext(node, ContextKeyEnum.SYSTEM_PROMPT, systemPrompt);
            
            // 2.生成DAG 任务节点  任务节点是 节点当前 Node 后面。 结束节点之前。
            PlanResult planResult = plan(systemPrompt, context.get(ContextKeyEnum.USER_MESSAGE));
            
            if (planResult == null) {
                log.error("生成任务规划失败，节点ID: {}", node.getId());
                node.setStatus(Node.NodeStatus.FAILED);
                nodeMapper.updateById(node);
                contextService.saveNodeContext(node, ContextKeyEnum.RESULT, "规划生成失败");
                return;
            }
            
            // 3. 更改当前计划节点状态为完成，保存规划结果
            node.setStatus(Node.NodeStatus.COMPLETED);
            nodeMapper.updateById(node);
            
            // 保存规划结果到上下文
            try {
                String planJson = objectMapper.writeValueAsString(planResult);
                contextService.saveNodeContext(node, ContextKeyEnum.RESULT, planJson);
                log.debug("规划结果已保存: {}", planJson);
            } catch (JsonProcessingException e) {
                log.error("序列化规划结果失败", e);
                contextService.saveNodeContext(node, ContextKeyEnum.RESULT, "规划结果序列化失败");
                return;
            }
            
            // 4. 在当前计划节点后面接上 plan 节点
            createAndLinkPlanNodes(node, planResult);
            
            log.info("规划节点执行完成，节点ID: {}", node.getId());
            
        } catch (Exception e) {
            log.error("执行规划节点失败，节点ID: " + node.getId(), e);
            node.setStatus(Node.NodeStatus.FAILED);
            nodeMapper.updateById(node);
            contextService.saveNodeContext(node, ContextKeyEnum.RESULT, "规划执行异常: " + e.getMessage());
        }
    }

    /**
     * 生成任务规划
     * @param systemPrompt
     * @param userMessage
     * @return
     */
    public PlanResult plan(String systemPrompt, String userMessage){
        try {
            log.info("开始生成任务规划，用户消息: {}", userMessage);
            
            // 调用 LLM 生成规划
            String planResponse = llmCallService.callQwenModel(systemPrompt, userMessage);
            log.debug("LLM 规划响应: {}", planResponse);
            
            // 解析 JSON 响应
            JsonNode responseNode = objectMapper.readTree(planResponse);
            JsonNode planNode = responseNode.get("plan");
            
            if (planNode == null || planNode.isNull()) {
                log.warn("LLM 返回的规划为空或无效");
                return null;
            }
            
            // 解析节点列表
            JsonNode nodesArray = planNode.get("nodes");
            if (nodesArray == null || !nodesArray.isArray() || nodesArray.isEmpty()) {
                log.warn("规划中没有有效的节点列表");
                return null;
            }
            
            // 获取开始节点ID和结束节点ID
            String startNodeId = planNode.get("start_node").asText();
            String endNodeId = planNode.get("end_node").asText();
            
            // 解析所有节点
            List<PlanNode> nodes = new ArrayList<>();
            for (JsonNode nodeJson : nodesArray) {
                PlanNode node = parsePlanNode(nodeJson);
                nodes.add(node);
            }
            
            // 创建并返回规划结果
            PlanResult result = new PlanResult();
            result.setStartNode(startNodeId);
            result.setEndNode(endNodeId);
            result.setNodes(nodes);
            
            log.info("成功生成任务规划，包含 {} 个节点", nodes.size());
            return result;
            
        } catch (JsonProcessingException e) {
            log.error("解析 LLM 响应 JSON 失败", e);
            return null;
        } catch (Exception e) {
            log.error("生成任务规划失败", e);
            return null;
        }
    }



    /**
     * 创建并链接规划节点到当前节点后面
     */
    private void createAndLinkPlanNodes(Node currentNode, PlanResult planResult) {
        try {
            log.info("开始创建规划节点，当前节点ID: {}", currentNode.getId());
            
            // 获取当前节点的下一个节点（通常是结束节点）
            Node nextNode = null;
            if (currentNode.getNextNodeId() != null) {
                nextNode = nodeMapper.selectById(currentNode.getNextNodeId());
            }
            
            // 创建规划中的节点
            Map<String, Node> createdNodes = new HashMap<>();
            Node firstPlanNode = null;
            Node lastPlanNode = null;
            
            // 创建所有规划节点
            for (PlanNode planNode : planResult.getNodes()) {
                Node newNode = new Node();
                newNode.setAgentName(planNode.getAgent());
                newNode.setStatus(Node.NodeStatus.PENDING);
                newNode.setUserId(currentNode.getUserId());
                newNode.setTaskId(currentNode.getTaskId());  // 传递任务ID
                
                // 插入节点到数据库
                nodeMapper.insert(newNode);
                createdNodes.put(planNode.getId(), newNode);
                
                // 保存节点任务信息到上下文
                contextService.saveNodeContext(newNode, ContextKeyEnum.USER_MESSAGE, planNode.getTask());

                // 记录第一个和最后一个节点
                if (planNode.getId().equals(planResult.getStartNode())) {
                    firstPlanNode = newNode;
                }
                if (planNode.getNext() == null || planNode.getNext().isEmpty()) {
                    lastPlanNode = newNode;
                }
                
                log.debug("创建规划节点: ID={}, Agent={}", 
                    newNode.getId(), newNode.getAgentName());
            }
            
            // 建立规划节点之间的链接关系
            for (PlanNode planNode : planResult.getNodes()) {
                Node currentPlanNode = createdNodes.get(planNode.getId());
                if (planNode.getNext() != null && !planNode.getNext().isEmpty()) {
                    // 取第一个 next 节点作为链接目标（简化处理）
                    String nextNodeId = planNode.getNext().get(0);
                    Node nextPlanNode = createdNodes.get(nextNodeId);
                    if (nextPlanNode != null) {
                        currentPlanNode.setNextNodeId(nextPlanNode.getId());
                        nodeMapper.updateById(currentPlanNode);
                        log.debug("链接节点: {} -> {}", currentPlanNode.getId(), nextPlanNode.getId());
                    }
                }
            }
            
            // 将当前节点链接到第一个规划节点
            if (firstPlanNode != null) {
                currentNode.setNextNodeId(firstPlanNode.getId());
                nodeMapper.updateById(currentNode);
                log.debug("当前节点链接到第一个规划节点: {} -> {}", currentNode.getId(), firstPlanNode.getId());
            }
            
            // 将最后一个规划节点链接到原来的下一个节点
            if (lastPlanNode != null && nextNode != null) {
                lastPlanNode.setNextNodeId(nextNode.getId());
                nodeMapper.updateById(lastPlanNode);
                log.debug("最后规划节点链接到原下一个节点: {} -> {}", lastPlanNode.getId(), nextNode.getId());
            }
            
            log.info("规划节点创建和链接完成，共创建 {} 个节点", createdNodes.size());
            
        } catch (Exception e) {
            log.error("创建和链接规划节点失败", e);
            throw new RuntimeException("创建规划节点失败", e);
        }
    }

    private PlanNode parsePlanNode(JsonNode nodeJson) {
        PlanNode planNode = new PlanNode();
        planNode.setId(nodeJson.get("id").asText());
        planNode.setTask(nodeJson.get("task").asText());
        
        // 设置 agent（可能为空）
        JsonNode agentNode = nodeJson.get("agent");
        if (agentNode != null && !agentNode.isNull()) {
            planNode.setAgent(agentNode.asText());
        }

        // 解析 next 列表
        JsonNode nextNode = nodeJson.get("next");
        if (nextNode != null && nextNode.isArray()) {
            List<String> nextList = new ArrayList<>();
            nextNode.forEach(node -> nextList.add(node.asText()));
            planNode.setNext(nextList);
        }
        
        // 解析 prev 列表
        JsonNode prevNode = nodeJson.get("prev");
        if (prevNode != null && prevNode.isArray()) {
            List<String> prevList = new ArrayList<>();
            prevNode.forEach(node -> prevList.add(node.asText()));
            planNode.setPrev(prevList);
        }
        
        return planNode;
    }

    public static class PlanNode {
        private String id;
        private String agent;
        private String task;
        private List<String> next;
        private List<String> prev;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        

        
        public String getAgent() { return agent; }
        public void setAgent(String agent) { this.agent = agent; }
        
        public String getTask() { return task; }
        public void setTask(String task) { this.task = task; }

        
        public List<String> getNext() { return next; }
        public void setNext(List<String> next) { this.next = next; }
        
        public List<String> getPrev() { return prev; }
        public void setPrev(List<String> prev) { this.prev = prev; }
    }
    
    public static class PlanResult {
        private String startNode;
        private String endNode;
        private List<PlanNode> nodes;
        
        // Getters and Setters
        public String getStartNode() { return startNode; }
        public void setStartNode(String startNode) { this.startNode = startNode; }
        
        public String getEndNode() { return endNode; }
        public void setEndNode(String endNode) { this.endNode = endNode; }
        
        public List<PlanNode> getNodes() { return nodes; }
        public void setNodes(List<PlanNode> nodes) { this.nodes = nodes; }
    }












}
