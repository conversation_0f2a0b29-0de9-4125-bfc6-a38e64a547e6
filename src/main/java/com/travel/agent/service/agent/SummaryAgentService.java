package com.travel.agent.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.travel.agent.entity.Node;
import com.travel.agent.entity.NodeContext;
import com.travel.agent.enums.ContextKeyEnum;
import com.travel.agent.mapper.NodeMapper;
import com.travel.agent.mapper.NodeContextMapper;
import com.travel.agent.service.ContextService;
import com.travel.agent.service.LLMCallService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class SummaryAgentService {


    private static final Logger log = LoggerFactory.getLogger(SummaryAgentService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private NodeMapper nodeMapper;
    @Resource
    private NodeContextMapper nodeContextMapper;
    @Autowired
    private LLMCallService llmCallService;
    @Autowired
    private ContextService contextService;


    /**


     role: system
     name: summary_agent
     purpose: >
     汇总多个 agent 的结果，并结合用户原始问题生成自然语言回答。

     core_responsibilities:
     - 读取前序节点的输出。
     - 理解用户的原始问题。
     - 将结果进行语义整合、推理与总结。
     - 输出最终自然语言形式的回答。
     - 不再触发新的外部查询。



     */




    public void run(Node node){
        try {
            log.info("开始执行汇总节点，节点ID: {}", node.getId());
            
            // 更新节点状态为运行中
            node.setStatus(Node.NodeStatus.RUNNING);
            nodeMapper.updateById(node);
            
            // 1. 查询所有前驱节点的执行状态和结果
            StringBuilder predecessorResults = new StringBuilder();
            String originalUserMessage = collectPredecessorResults(node, predecessorResults);
            
            // 2. 获取系统提示词
            Map<ContextKeyEnum, String> context = contextService.getRunNodeContext(node);
            String systemPrompt = context.get(ContextKeyEnum.SYSTEM_PROMPT);
            
            // 3. 构建汇总输入消息
            String summaryInput = buildSummaryInput(originalUserMessage, predecessorResults.toString());
            contextService.saveNodeContext(node, ContextKeyEnum.USER_MESSAGE, summaryInput);
            
            // 4. 调用LLM进行汇总
            log.debug("调用LLM进行结果汇总，系统提示词: {}", systemPrompt);
            log.debug("汇总输入: {}", summaryInput);
            
            String summaryResult = llmCallService.callQwenModel(systemPrompt, summaryInput);
            log.debug("LLM汇总结果: {}", summaryResult);
            
            // 5. 更新节点状态为完成
            node.setStatus(Node.NodeStatus.COMPLETED);
            nodeMapper.updateById(node);
            
            // 6. 保存汇总结果
            contextService.saveNodeContext(node, ContextKeyEnum.RESULT, summaryResult);
            
            log.info("汇总节点执行完成，节点ID: {}", node.getId());
            
        } catch (Exception e) {
            log.error("执行汇总节点失败，节点ID: " + node.getId(), e);
            node.setStatus(Node.NodeStatus.FAILED);
            nodeMapper.updateById(node);
            contextService.saveNodeContext(node, ContextKeyEnum.RESULT, "汇总执行异常: " + e.getMessage());
        }
    }
    
    /**
     * 收集前序节点的执行结果
     * @param node 当前汇总节点
     * @param predecessorResults 用于收集前序节点结果的StringBuilder
     * @return 用户原始问题
     */
    private String collectPredecessorResults(Node node, StringBuilder predecessorResults) {
        String originalUserMessage = "";
        
        try {
            // 获取同一任务中的所有节点
            List<Node> taskNodes = nodeMapper.findByTaskId(node.getTaskId());
            log.debug("任务ID: {} 中共有 {} 个节点", node.getTaskId(), taskNodes.size());
            
            // 按创建时间排序，确保按执行顺序处理
            taskNodes.sort((n1, n2) -> n1.getCreatedAt().compareTo(n2.getCreatedAt()));
            
            for (Node taskNode : taskNodes) {
                // 跳过当前汇总节点本身
                if (taskNode.getId().equals(node.getId())) {
                    continue;
                }
                
                // 获取节点的上下文信息
                List<NodeContext> nodeContexts = nodeContextMapper.findByNodeId(taskNode.getId());
                
                // 查找用户消息（原始问题）
                if ("start".equals(taskNode.getAgentName()) || "plan_agent".equals(taskNode.getAgentName())) {
                    for (NodeContext context : nodeContexts) {
                        if (ContextKeyEnum.USER_MESSAGE.getCode().equals(context.getContextKey())) {
                            originalUserMessage = context.getContextValue();
                            log.debug("找到用户原始问题: {}", originalUserMessage);
                            break;
                        }
                    }
                }
                
                // 收集已完成节点的结果
                if (Node.NodeStatus.COMPLETED.equals(taskNode.getStatus())) {
                    for (NodeContext context : nodeContexts) {
                        if (ContextKeyEnum.RESULT.getCode().equals(context.getContextKey())) {
                            predecessorResults.append("【").append(taskNode.getAgentName()).append("执行结果】\n");
                            predecessorResults.append(context.getContextValue()).append("\n\n");
                            log.debug("收集到节点 {} 的执行结果", taskNode.getAgentName());
                            break;
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("收集前序节点结果失败", e);
            throw new RuntimeException("收集前序节点结果失败", e);
        }
        
        return originalUserMessage;
    }
    
    /**
     * 构建汇总输入消息
     * @param originalUserMessage 用户原始问题
     * @param predecessorResults 前序节点执行结果
     * @return 汇总输入消息
     */
    private String buildSummaryInput(String originalUserMessage, String predecessorResults) {
        StringBuilder summaryInput = new StringBuilder();
        
        summaryInput.append("用户原始问题：\n");
        summaryInput.append(originalUserMessage).append("\n\n");
        
        summaryInput.append("各个Agent的执行结果：\n");
        summaryInput.append(predecessorResults);
        
        summaryInput.append("请基于以上信息，结合用户的原始问题，生成一个完整、自然、有用的回答。");
        
        return summaryInput.toString();
    }











}
