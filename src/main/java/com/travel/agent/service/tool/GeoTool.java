package com.travel.agent.service.tool;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 地理位置工具服务
 * 使用 Streamable HTTP 连接 AMap MCP Server
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Service
public class GeoTool {

    private static final Logger log = LoggerFactory.getLogger(GeoTool.class);

    @Value("${amap.mcp.url:https://mcp.amap.com/mcp?key=7577a5470ca74e0d818841103c5e7b7f}")
    private String amapMcpUrl;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    // 本地内存缓存
    private final Map<String, ToolSchema> toolSchemaCache = new ConcurrentHashMap<>();

    public GeoTool() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Bean 初始化后连接 AMap MCP Server 并拉取工具方法
     */
    @PostConstruct
    public void initializeTools() {
        log.info("开始初始化 GeoTool，连接 AMap MCP Server: {}", amapMcpUrl);
        try {
            fetchAndCacheToolSchemas();
            log.info("GeoTool 初始化完成，共缓存 {} 个工具", toolSchemaCache.size());
        } catch (Exception e) {
            log.error("GeoTool 初始化失败", e);
        }
    }







    /**
     * 从 AMap MCP Server 拉取并缓存工具模式
     */
    private void fetchAndCacheToolSchemas() {
        try {
            log.info("开始从 AMap MCP Server 拉取工具模式");
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 设置 Accept 头，同时接受 application/json 和 text/event-stream
            List<MediaType> acceptableMediaTypes = Arrays.asList(
                MediaType.APPLICATION_JSON,
                MediaType.valueOf("text/event-stream")
            );
            headers.setAccept(acceptableMediaTypes);
            
            // 构建 JSON-RPC 格式的请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("method", "tools/list");
            requestBody.put("id", 1);
            
            // 创建请求实体（包含请求体）
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // 发送 POST 请求获取工具列表
            ResponseEntity<String> response = restTemplate.exchange(
                amapMcpUrl + "/tools/list",
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                parseAndCacheToolSchemas(response.getBody());
            } else {
                log.warn("从 AMap MCP Server 获取工具列表失败，状态码: {}", response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("连接 AMap MCP Server 失败", e);
        }
    }

    /**
     * 解析并缓存工具模式
     */
    private void parseAndCacheToolSchemas(String responseBody) {
        try {
            JsonNode rootNode = objectMapper.readTree(responseBody);
            JsonNode toolsNode = rootNode.get("tools");
            
            if (toolsNode != null && toolsNode.isArray()) {
                for (JsonNode toolNode : toolsNode) {
                    ToolSchema schema = parseToolSchema(toolNode);
                    if (schema != null) {
                        toolSchemaCache.put(schema.getName(), schema);
                        log.debug("缓存工具模式: {}", schema.getName());
                    }
                }
            }
        } catch (JsonProcessingException e) {
            log.error("解析工具模式响应失败", e);
        }
    }

    /**
     * 解析单个工具模式
     */
    private ToolSchema parseToolSchema(JsonNode toolNode) {
        try {
            String name = toolNode.get("name").asText();
            String description = toolNode.get("description").asText();
            String endpoint = toolNode.get("endpoint").asText();
            String method = toolNode.has("method") ? toolNode.get("method").asText() : "POST";
            
            Map<String, Object> parameters = new HashMap<>();
            JsonNode paramsNode = toolNode.get("parameters");
            if (paramsNode != null) {
                parameters = objectMapper.convertValue(paramsNode, Map.class);
            }
            
            return new ToolSchema(name, description, parameters, endpoint, method);
        } catch (Exception e) {
            log.error("解析工具模式失败", e);
            return null;
        }
    }


     /**
      * 获取所有工具的标准 Function Call 格式
      * 
      * @return Function Call 格式的工具列表
      */
     public List<FunctionCallTool> getFunctionCallTools() {
         List<FunctionCallTool> tools = new ArrayList<>();
         
         for (ToolSchema schema : toolSchemaCache.values()) {
             FunctionDefinition function = new FunctionDefinition(
                 schema.getName(),
                 schema.getDescription(),
                 schema.getParameters()
             );
             tools.add(new FunctionCallTool(function));
         }
         
         log.debug("返回 {} 个 Function Call 格式的工具", tools.size());
         return tools;
     }

     /**
      * 获取指定工具的 Function Call 格式
      * 
      * @param toolName 工具名称
      * @return Function Call 格式的工具，如果不存在返回 null
      */
     public FunctionCallTool getFunctionCallTool(String toolName) {
         ToolSchema schema = toolSchemaCache.get(toolName);
         if (schema != null) {
             FunctionDefinition function = new FunctionDefinition(
                 schema.getName(),
                 schema.getDescription(),
                 schema.getParameters()
             );
             return new FunctionCallTool(function);
         }
         return null;
     }

     /**
      * 调用指定的工具方法
      * 
      * @param toolName 工具名称
      * @param parameters 调用参数
      * @return 工具调用结果
      */
     public ToolCallResult callTool(String toolName, Map<String, Object> parameters) {
         long startTime = System.currentTimeMillis();
         
         try {
             ToolSchema schema = toolSchemaCache.get(toolName);
             if (schema == null) {
                 return new ToolCallResult(false, toolName, null, 
                     "工具不存在: " + toolName, System.currentTimeMillis() - startTime);
             }

             log.info("调用工具: {}, 参数: {}", toolName, parameters);
             
             // 验证必需参数
             String validationError = validateParameters(schema, parameters);
             if (validationError != null) {
                 return new ToolCallResult(false, toolName, null, 
                     validationError, System.currentTimeMillis() - startTime);
             }

             // 执行工具调用
             Object result = executeToolCall(schema, parameters);
             
             long executionTime = System.currentTimeMillis() - startTime;
             log.info("工具调用完成: {}, 耗时: {}ms", toolName, executionTime);
             
             return new ToolCallResult(true, toolName, result, null, executionTime);
             
         } catch (Exception e) {
             long executionTime = System.currentTimeMillis() - startTime;
             log.error("工具调用失败: " + toolName, e);
             return new ToolCallResult(false, toolName, null, 
                 "调用失败: " + e.getMessage(), executionTime);
         }
     }

     /**
      * 验证调用参数
      */
     private String validateParameters(ToolSchema schema, Map<String, Object> parameters) {
         Map<String, Object> schemaParams = schema.getParameters();
         if (schemaParams == null) {
             return null;
         }

         @SuppressWarnings("unchecked")
         List<String> required = (List<String>) schemaParams.get("required");
         if (required != null) {
             for (String requiredParam : required) {
                 if (!parameters.containsKey(requiredParam) || parameters.get(requiredParam) == null) {
                     return "缺少必需参数: " + requiredParam;
                 }
             }
         }
         
         return null;
     }

     /**
      * 执行具体的工具调用
      */
     private Object executeToolCall(ToolSchema schema, Map<String, Object> parameters) {
         return callAmapApi(schema, parameters);
     }




     /**
      * 调用真实的 AMap API
      */
     private Object callAmapApi(ToolSchema schema, Map<String, Object> parameters) {
         try {
             // 构建请求 URL
             String url = amapMcpUrl + schema.getEndpoint();
             
             // 设置请求头
             HttpHeaders headers = new HttpHeaders();
             headers.setContentType(MediaType.APPLICATION_JSON);
             
             HttpEntity<?> requestEntity;
             ResponseEntity<String> response;
             
             if ("GET".equalsIgnoreCase(schema.getMethod())) {
                 // GET 请求，参数放在 URL 中
                 StringBuilder urlBuilder = new StringBuilder(url);
                 if (!parameters.isEmpty()) {
                     urlBuilder.append("?");
                     parameters.forEach((key, value) -> 
                         urlBuilder.append(key).append("=").append(value).append("&"));
                 }
                 
                 requestEntity = new HttpEntity<>(headers);
                 response = restTemplate.exchange(
                     urlBuilder.toString(),
                     HttpMethod.GET,
                     requestEntity,
                     String.class
                 );
             } else {
                 // POST 请求，参数放在请求体中
                 requestEntity = new HttpEntity<>(parameters, headers);
                 response = restTemplate.exchange(
                     url,
                     HttpMethod.POST,
                     requestEntity,
                     String.class
                 );
             }
             
             if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                 return objectMapper.readValue(response.getBody(), Object.class);
             } else {
                 throw new RuntimeException("API 调用失败，状态码: " + response.getStatusCode());
             }
             
         } catch (Exception e) {
             log.error("调用 AMap API 失败: " + schema.getName(), e);
             throw new RuntimeException("API 调用失败: " + e.getMessage());
         }
     }

     /**
      * 获取所有可用的工具名称
      * 
      * @return 工具名称列表
      */
     public Set<String> getAvailableToolNames() {
         return new HashSet<>(toolSchemaCache.keySet());
     }

     /**
      * 获取工具的详细信息
      * 
      * @param toolName 工具名称
      * @return 工具模式，如果不存在返回 null
      */
     public ToolSchema getToolSchema(String toolName) {
         return toolSchemaCache.get(toolName);
     }

     /**
      * 检查工具是否存在
      * 
      * @param toolName 工具名称
      * @return 是否存在
      */
     public boolean hasToolSchema(String toolName) {
         return toolSchemaCache.containsKey(toolName);
     }
}
