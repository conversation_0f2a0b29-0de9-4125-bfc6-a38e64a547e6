package com.travel.agent.service.tool;

import java.util.Map;

public class ToolSchema {
    private String name;
    private String description;
    private Map<String, Object> parameters;
    private String endpoint;
    private String method;

    public ToolSchema() {}

    public ToolSchema(String name, String description, Map<String, Object> parameters, String endpoint, String method) {
        this.name = name;
        this.description = description;
        this.parameters = parameters;
        this.endpoint = endpoint;
        this.method = method;
    }

    // Getters and Setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    public String getEndpoint() { return endpoint; }
    public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
    public String getMethod() { return method; }
    public void setMethod(String method) { this.method = method; }
}
