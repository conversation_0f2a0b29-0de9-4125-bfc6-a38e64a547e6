package com.travel.agent.service.tool;

public class ToolCallResult {
    private boolean success;
    private String toolName;
    private Object data;
    private String error;
    private long executionTime;

    public ToolCallResult(boolean success, String toolName, Object data, String error, long executionTime) {
        this.success = success;
        this.toolName = toolName;
        this.data = data;
        this.error = error;
        this.executionTime = executionTime;
    }

    // Getters and Setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    public String getToolName() { return toolName; }
    public void setToolName(String toolName) { this.toolName = toolName; }
    public Object getData() { return data; }
    public void setData(Object data) { this.data = data; }
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    public long getExecutionTime() { return executionTime; }
    public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
}
