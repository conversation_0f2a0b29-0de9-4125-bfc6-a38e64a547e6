package com.travel.agent.service.tool;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网络搜索工具服务
 * 提供网络搜索、新闻搜索、URL内容提取和思考功能
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Service
public class WebSearchTool {

    private static final Logger log = LoggerFactory.getLogger(WebSearchTool.class);

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    // 本地内存缓存
    private final Map<String, ToolCallResult> resultCache = new ConcurrentHashMap<>();

    public WebSearchTool() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 工具调用结果
     */
    public static class ToolCallResult {
        private boolean success;
        private String toolName;
        private Object data;
        private String error;
        private long executionTime;

        public ToolCallResult(boolean success, String toolName, Object data, String error, long executionTime) {
            this.success = success;
            this.toolName = toolName;
            this.data = data;
            this.error = error;
            this.executionTime = executionTime;
        }

        public boolean isSuccess() { return success; }
        public String getToolName() { return toolName; }
        public Object getData() { return data; }
        public String getError() { return error; }
        public long getExecutionTime() { return executionTime; }
    }

    /**
     * 搜索结果项
     */
    public static class SearchResult {
        private String title;
        private String url;
        private String snippet;

        public SearchResult(String title, String url, String snippet) {
            this.title = title;
            this.url = url;
            this.snippet = snippet;
        }

        public String getTitle() { return title; }
        public String getUrl() { return url; }
        public String getSnippet() { return snippet; }
    }

    /**
     * 获取指定工具的 Function Call 格式
     */
    public FunctionCallTool getFunctionCallTool(String toolName) {
        switch (toolName) {
            case "search_web":
                return createSearchWebTool();
            case "search_news":
                return createSearchNewsTool();
            case "extract_content_from_url":
                return createExtractContentTool();
            case "think":
                return createThinkTool();
            default:
                return null;
        }
    }

    /**
     * 创建网络搜索工具
     */
    private FunctionCallTool createSearchWebTool() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("query", Map.of(
            "type", "string",
            "description", "搜索查询关键词"
        ));
        properties.put("max_results", Map.of(
            "type", "integer",
            "description", "最大结果数量，默认为5",
            "default", 5
        ));

        Map<String, Object> parameters = Map.of(
            "type", "object",
            "properties", properties,
            "required", List.of("query")
        );

        FunctionDefinition function = new FunctionDefinition(
            "search_web",
            "在网络上搜索一般信息",
            parameters
        );
        return new FunctionCallTool(function);
    }

    /**
     * 创建新闻搜索工具
     */
    private FunctionCallTool createSearchNewsTool() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("query", Map.of(
            "type", "string",
            "description", "新闻搜索查询关键词"
        ));
        properties.put("max_results", Map.of(
            "type", "integer",
            "description", "最大结果数量，默认为5",
            "default", 5
        ));

        Map<String, Object> parameters = Map.of(
            "type", "object",
            "properties", properties,
            "required", List.of("query")
        );

        FunctionDefinition function = new FunctionDefinition(
            "search_news",
            "搜索新闻信息",
            parameters
        );
        return new FunctionCallTool(function);
    }

    /**
     * 创建URL内容提取工具
     */
    private FunctionCallTool createExtractContentTool() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("url", Map.of(
            "type", "string",
            "description", "要提取内容的URL地址"
        ));

        Map<String, Object> parameters = Map.of(
            "type", "object",
            "properties", properties,
            "required", List.of("url")
        );

        FunctionDefinition function = new FunctionDefinition(
            "extract_content_from_url",
            "从指定URL提取内容",
            parameters
        );
        return new FunctionCallTool(function);
    }

    /**
     * 创建思考工具
     */
    private FunctionCallTool createThinkTool() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("thought", Map.of(
            "type", "string",
            "description", "思考内容和分析"
        ));

        Map<String, Object> parameters = Map.of(
            "type", "object",
            "properties", properties,
            "required", List.of("thought")
        );

        FunctionDefinition function = new FunctionDefinition(
            "think",
            "思考和分析工具，用于深度思考和推理",
            parameters
        );
        return new FunctionCallTool(function);
    }

    /**
     * 调用工具
     */
    public ToolCallResult callTool(String toolName, Map<String, Object> parameters) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始调用工具: {}, 参数: {}", toolName, parameters);
            
            Object result;
            switch (toolName) {
                case "search_web":
                    result = searchWeb(parameters);
                    break;
                case "search_news":
                    result = searchNews(parameters);
                    break;
                case "extract_content_from_url":
                    result = extractContentFromUrl(parameters);
                    break;
                case "think":
                    result = think(parameters);
                    break;
                default:
                    throw new IllegalArgumentException("未知的工具名称: " + toolName);
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            log.info("工具调用成功: {}, 执行时间: {}ms", toolName, executionTime);
            
            return new ToolCallResult(true, toolName, result, null, executionTime);
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("工具调用失败: {}, 错误: {}", toolName, e.getMessage(), e);
            
            return new ToolCallResult(false, toolName, null, e.getMessage(), executionTime);
        }
    }

    /**
     * 网络搜索实现（使用DuckDuckGo）
     */
    private Object searchWeb(Map<String, Object> parameters) {
        String query = (String) parameters.get("query");
        Integer maxResults = (Integer) parameters.getOrDefault("max_results", 5);
        
        if (query == null || query.trim().isEmpty()) {
            throw new IllegalArgumentException("搜索查询不能为空");
        }

        try {
            // 使用DuckDuckGo搜索
            String searchUrl = "https://duckduckgo.com/html/?q=" + URLEncoder.encode(query, StandardCharsets.UTF_8);
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(searchUrl, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                return parseSearchResults(response.getBody(), maxResults);
            } else {
                throw new RuntimeException("搜索请求失败，状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("网络搜索失败: {}", e.getMessage(), e);
            // 返回模拟结果作为降级方案
            return createFallbackSearchResults(query, maxResults);
        }
    }

    /**
     * 新闻搜索实现
     */
    private Object searchNews(Map<String, Object> parameters) {
        String query = (String) parameters.get("query");
        Integer maxResults = (Integer) parameters.getOrDefault("max_results", 5);
        
        if (query == null || query.trim().isEmpty()) {
            throw new IllegalArgumentException("新闻搜索查询不能为空");
        }

        try {
            // 使用DuckDuckGo新闻搜索
            String newsQuery = query + " site:news.google.com OR site:bbc.com OR site:cnn.com";
            String searchUrl = "https://duckduckgo.com/html/?q=" + URLEncoder.encode(newsQuery, StandardCharsets.UTF_8);
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(searchUrl, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                return parseSearchResults(response.getBody(), maxResults);
            } else {
                throw new RuntimeException("新闻搜索请求失败，状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("新闻搜索失败: {}", e.getMessage(), e);
            // 返回模拟结果作为降级方案
            return createFallbackNewsResults(query, maxResults);
        }
    }

    /**
     * URL内容提取实现
     */
    private Object extractContentFromUrl(Map<String, Object> parameters) {
        String url = (String) parameters.get("url");
        
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }

        try {
            // 使用Jsoup提取网页内容
            Document doc = Jsoup.connect(url)
                .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .timeout(10000)
                .get();
            
            String title = doc.title();
            String content = doc.body().text();
            
            // 限制内容长度
            if (content.length() > 2000) {
                content = content.substring(0, 2000) + "...";
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("title", title);
            result.put("content", content);
            result.put("extracted_at", new Date());
            
            return result;
            
        } catch (Exception e) {
            log.error("URL内容提取失败: {}", e.getMessage(), e);
            throw new RuntimeException("无法提取URL内容: " + e.getMessage());
        }
    }

    /**
     * 思考工具实现
     */
    private Object think(Map<String, Object> parameters) {
        String thought = (String) parameters.get("thought");
        
        if (thought == null || thought.trim().isEmpty()) {
            throw new IllegalArgumentException("思考内容不能为空");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("thought", thought);
        result.put("timestamp", new Date());
        result.put("analysis", "思考已记录，继续分析...");
        
        return result;
    }

    /**
     * 解析搜索结果
     */
    private List<SearchResult> parseSearchResults(String html, int maxResults) {
        List<SearchResult> results = new ArrayList<>();
        
        try {
            Document doc = Jsoup.parse(html);
            Elements resultElements = doc.select(".result");
            
            int count = 0;
            for (Element element : resultElements) {
                if (count >= maxResults) break;
                
                Element titleElement = element.selectFirst(".result__title a");
                Element snippetElement = element.selectFirst(".result__snippet");
                
                if (titleElement != null) {
                    String title = titleElement.text();
                    String url = titleElement.attr("href");
                    String snippet = snippetElement != null ? snippetElement.text() : "";
                    
                    results.add(new SearchResult(title, url, snippet));
                    count++;
                }
            }
            
        } catch (Exception e) {
            log.error("解析搜索结果失败: {}", e.getMessage(), e);
        }
        
        return results;
    }

    /**
     * 创建降级搜索结果
     */
    private List<SearchResult> createFallbackSearchResults(String query, int maxResults) {
        List<SearchResult> results = new ArrayList<>();
        
        for (int i = 1; i <= Math.min(maxResults, 3); i++) {
            results.add(new SearchResult(
                "搜索结果 " + i + " - " + query,
                "https://example.com/result" + i,
                "关于 " + query + " 的搜索结果摘要 " + i
            ));
        }
        
        return results;
    }

    /**
     * 创建降级新闻结果
     */
    private List<SearchResult> createFallbackNewsResults(String query, int maxResults) {
        List<SearchResult> results = new ArrayList<>();
        
        for (int i = 1; i <= Math.min(maxResults, 3); i++) {
            results.add(new SearchResult(
                "新闻标题 " + i + " - " + query,
                "https://news.example.com/article" + i,
                "关于 " + query + " 的新闻摘要 " + i
            ));
        }
        
        return results;
    }

    /**
     * 获取所有可用的工具名称
     */
    public Set<String> getAvailableToolNames() {
        return Set.of("search_web", "search_news", "extract_content_from_url", "think");
    }

    /**
     * 检查工具是否存在
     */
    public boolean hasToolSchema(String toolName) {
        return getAvailableToolNames().contains(toolName);
    }
}
