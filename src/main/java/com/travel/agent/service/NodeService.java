package com.travel.agent.service;

import com.travel.agent.entity.Node;
import com.travel.agent.enums.ContextKeyEnum;
import com.travel.agent.mapper.NodeMapper;
import com.travel.agent.mapper.NodeContextMapper;
import com.travel.agent.service.agent.PlanAgentService;
import com.travel.agent.service.agent.GeoAgentService;
import com.travel.agent.service.agent.WebSearchAgentService;
import com.travel.agent.util.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class NodeService {

    private static final Logger log = LoggerFactory.getLogger(NodeService.class);

    @Resource
    private NodeMapper nodeMapper;
    
    @Resource
    private NodeContextMapper nodeContextMapper;
    
    @Resource
    private ContextService contextService;
    
    @Resource
    private PlanAgentService planAgentService;
    
    @Resource
    private GeoAgentService geoAgentService;

    @Resource
    private WebSearchAgentService webSearchAgentService;






    public void init(String userMessage){
        /**
          初始化DAG 节点。  创建 开始节点  - 规划任务节点（使用plan_agent 执行的节点， 任务就是用户输入的信息 ）  - 结束节点

         节点状态， 开始和结束都是  completed ， 规划任务节点 是 pending 初始化状态。

         结构参考 com.travel.agent.entity.Node
         */
        
        try {
            log.info("开始初始化DAG节点，用户消息: {}", userMessage);
            
            // 获取当前用户ID
            Long userId = UserContext.getCurrentUserId();
            if (userId == null) {
                throw new RuntimeException("用户未登录，无法初始化DAG节点");
            }
            
            // 1. 创建开始节点
            Node startNode = new Node();
            startNode.setUserId(userId);
            startNode.setAgentName("start");
            startNode.setStatus(Node.NodeStatus.COMPLETED);
            nodeMapper.insert(startNode);
            
            // 设置任务ID为开始节点的ID
            Long taskId = startNode.getId();
            startNode.setTaskId(taskId);
            nodeMapper.updateById(startNode);
            log.debug("创建开始节点: {}, 任务ID: {}", startNode.getId(), taskId);
            
            // 2. 创建规划任务节点
            Node planNode = new Node();
            planNode.setUserId(userId);
            planNode.setTaskId(taskId);  // 设置任务ID
            planNode.setPrevNodeId(startNode.getId());
            planNode.setAgentName("plan_agent");
            planNode.setStatus(Node.NodeStatus.PENDING);
            nodeMapper.insert(planNode);
            log.debug("创建规划节点: {}, 任务ID: {}", planNode.getId(), taskId);
            
            // 保存用户消息到规划节点的上下文
            contextService.saveNodeContext(planNode, ContextKeyEnum.USER_MESSAGE, userMessage);
            
            // 3. 创建结束节点
            Node endNode = new Node();
            endNode.setUserId(userId);
            endNode.setTaskId(taskId);  // 设置任务ID
            endNode.setPrevNodeId(planNode.getId());
            endNode.setAgentName("end");
            endNode.setStatus(Node.NodeStatus.COMPLETED);
            nodeMapper.insert(endNode);
            log.debug("创建结束节点: {}, 任务ID: {}", endNode.getId(), taskId);
            
            // 4. 建立节点链接关系
            startNode.setNextNodeId(planNode.getId());
            nodeMapper.updateById(startNode);
            
            planNode.setNextNodeId(endNode.getId());
            nodeMapper.updateById(planNode);
            
            log.info("DAG节点初始化完成，开始节点: {}, 规划节点: {}, 结束节点: {}", 
                startNode.getId(), planNode.getId(), endNode.getId());
                
        } catch (Exception e) {
            log.error("初始化DAG节点失败", e);
            throw new RuntimeException("初始化DAG节点失败", e);
        }
    }


    public void runNode(Long nodeId){

        /**
         运行指定节点信息。
         1. 重新初始化节点状态
         如果当前节点是规划节点， 则结束节点前的所有节点都删除。
         如果当前节点非规划节点， 则需要该节点以及后续的所有节点（除了结束节点） 全部初始化 （失效所有context_node信息。 状态全改为pending）

         2. 执行节点
         通过 节点的agentName 使用策略模式 获取对应的实现类 agent 然后调用run 方法。
         */
         
        try {
            log.info("开始运行节点，节点ID: {}", nodeId);
            
            // 获取当前节点
            Node currentNode = nodeMapper.selectById(nodeId);
            if (currentNode == null) {
                throw new RuntimeException("节点不存在: " + nodeId);
            }
            
            log.debug("当前节点信息: ID={}, Agent={}, Status={}", 
                currentNode.getId(), currentNode.getAgentName(), currentNode.getStatus());
            
            // 1. 重新初始化节点状态
            reinitializeNodeStatus(currentNode);
            
            // 2. 执行节点 - 按照DAG结构顺序执行剩余的所有节点
            executeNodesInSequence(currentNode);

        } catch (Exception e) {
            log.error("运行节点失败，节点ID: " + nodeId, e);
            throw new RuntimeException("运行节点失败", e);
        }
    }


    /**
     * 从某一节点后继续运行。 仅当 当前节点状态是 paused (需要人工确认的节点) 时， 才能调用该方法。
     * @param nodeId nodeId
     */
    public void continueRun(Long nodeId){
        // 获取当前节点
        Node currentNode = nodeMapper.selectById(nodeId);
        // 当前节点状态标记为完成
        currentNode.setStatus(Node.NodeStatus.COMPLETED);
        nodeMapper.updateById(currentNode);
        try{
            // 2. 执行节点 - 按照DAG结构顺序执行剩余的所有节点  (在确认情况下后续节点都是 pending ）
            executeNodesInSequence(currentNode);
        } catch (Exception e) {
            log.error("运行节点失败，节点ID: " + nodeId, e);
            throw new RuntimeException("运行节点失败", e);
        }
    }


    public void getResult(Long startNodeId){
        /**
         * TODO
         结合 开始节点用户输入的问题。
         以及结束节点前 所有的节点的结果
         得到最终的答案 ： 1.结束节点前节点只有一个， 则该节点的答案就是最终答案。
         2.结束节点前有多个节点。 则答案需要将开始节点用户问题和 结束节点前问题交给 plan agent 生成 最终答案。

         */




    }










    /**
     * 重新初始化节点状态
     */
    private void reinitializeNodeStatus(Node currentNode) {
        String agentName = currentNode.getAgentName();
        
        if ("plan_agent".equals(agentName)) {
            // 如果当前节点是规划节点，则删除结束节点前的所有节点（除了开始、规划、结束节点）
            deleteIntermediateNodes(currentNode);
        } else {
            // 如果当前节点非规划节点，则重置该节点以及后续的所有节点（除了结束节点）
            resetNodesFromCurrent(currentNode);
        }
    }
    
    /**
     * 删除中间节点（规划节点和结束节点之间的节点）
     */
    private void deleteIntermediateNodes(Node planNode) {
        try {
            log.debug("删除规划节点后的中间节点");
            
            // 找到结束节点
            Node endNode = findEndNode(planNode.getUserId());
            if (endNode == null) {
                log.warn("未找到结束节点");
                return;
            }
            
            // 删除规划节点和结束节点之间的所有节点
            Node currentNode = planNode;
            while (currentNode.getNextNodeId() != null && !currentNode.getNextNodeId().equals(endNode.getId())) {
                Node nextNode = nodeMapper.selectById(currentNode.getNextNodeId());
                if (nextNode != null) {
                    // 删除节点的上下文信息
                    nodeContextMapper.deleteByNodeId(nextNode.getId());
                    // 删除节点
                    nodeMapper.deleteById(nextNode.getId());
                    log.debug("删除中间节点: {}", nextNode.getId());
                    
                    currentNode = nextNode;
                } else {
                    break;
                }
            }
            
            // 重新链接规划节点到结束节点
            planNode.setNextNodeId(endNode.getId());
            nodeMapper.updateById(planNode);
            
            endNode.setPrevNodeId(planNode.getId());
            nodeMapper.updateById(endNode);
            
        } catch (Exception e) {
            log.error("删除中间节点失败", e);
            throw new RuntimeException("删除中间节点失败", e);
        }
    }
    
    /**
     * 重置当前节点及后续节点状态
     */
    private void resetNodesFromCurrent(Node currentNode) {
        try {
            log.debug("重置节点状态，从节点: {}", currentNode.getId());
            
            Node node = currentNode;
            while (node != null && !"end".equals(node.getAgentName())) {
                // 删除节点的上下文信息
                nodeContextMapper.deleteByNodeId(node.getId());
                
                // 重置节点状态为 PENDING
                node.setStatus(Node.NodeStatus.PENDING);
                nodeMapper.updateById(node);
                
                log.debug("重置节点状态: ID={}, Agent={}", node.getId(), node.getAgentName());
                
                // 移动到下一个节点
                if (node.getNextNodeId() != null) {
                    node = nodeMapper.selectById(node.getNextNodeId());
                } else {
                    break;
                }
            }
            
        } catch (Exception e) {
            log.error("重置节点状态失败", e);
            throw new RuntimeException("重置节点状态失败", e);
        }
    }
    
    /**
     * 查找结束节点
     */
    private Node findEndNode(Long userId) {
        List<Node> nodes = nodeMapper.findByUserId(userId);
        return nodes.stream()
            .filter(node -> "end".equals(node.getAgentName()))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 按照 DAG 结构顺序执行剩余的所有节点
     * 使用拓扑排序和依赖检查来实现真正的 DAG 执行
     */
    private void executeNodesInSequence(Node startNode) {
        try {
            log.info("开始按照 DAG 结构顺序执行节点，起始节点ID: {}", startNode.getId());
            
            // 获取用户的所有节点
            List<Node> allNodes = nodeMapper.findByTaskId(startNode.getTaskId());
            
            // 构建节点映射和依赖关系
            Map<Long, Node> nodeMap = new HashMap<>();
            Map<Long, List<Long>> successors = new HashMap<>(); // 后继节点映射
            Map<Long, Set<Long>> predecessors = new HashMap<>(); // 前驱节点映射
            
            for (Node node : allNodes) {
                nodeMap.put(node.getId(), node);
                successors.put(node.getId(), new ArrayList<>());
                predecessors.put(node.getId(), new HashSet<>());
            }
            
            // 构建依赖关系图
            for (Node node : allNodes) {
                if (node.getNextNodeId() != null && nodeMap.containsKey(node.getNextNodeId())) {
                    successors.get(node.getId()).add(node.getNextNodeId());
                    predecessors.get(node.getNextNodeId()).add(node.getId());
                }
            }
            
            // 使用队列进行拓扑排序执行
            Queue<Long> readyQueue = new LinkedList<>();
            Set<Long> executed = new HashSet<>();
            int executedCount = 0;
            
            // 将起始节点加入就绪队列
            readyQueue.offer(startNode.getId());
            
            while (!readyQueue.isEmpty()) {
                Long currentNodeId = readyQueue.poll();
                Node currentNode = nodeMap.get(currentNodeId);
                
                if (currentNode == null || executed.contains(currentNodeId)) {
                    continue;
                }
                
                // 检查是否所有前驱节点都已执行完成
                boolean allPredecessorsCompleted = true;
                for (Long predId : predecessors.get(currentNodeId)) {
                    Node predNode = nodeMap.get(predId);
                    if (predNode != null && !executed.contains(predId) && 
                        predNode.getStatus() != Node.NodeStatus.COMPLETED) {
                        allPredecessorsCompleted = false;
                        break;
                    }
                }
                
                if (!allPredecessorsCompleted) {
                    log.debug("节点 {} 的前驱节点尚未全部完成，暂时跳过", currentNodeId);
                    continue;
                }
                
                // 执行节点
                if (currentNode.getStatus() == Node.NodeStatus.PENDING) {
                    log.debug("执行节点: ID={}, Agent={}", currentNode.getId(), currentNode.getAgentName());
                    
                    // 更新节点状态为 RUNNING
                    currentNode.setStatus(Node.NodeStatus.RUNNING);
                    nodeMapper.updateById(currentNode);
                    
                    try {
                        // 执行节点
                        executeNode(currentNode);

                        // 如果当前节点状态为暂停， 则需要人工确认。
                        if (currentNode.getStatus() == Node.NodeStatus.PAUSED) {
                            log.debug("节点 {} 执行暂停，等待人工确认", currentNodeId);
                            continue;
                        }

                        executedCount++;
                        executed.add(currentNodeId);
                        log.debug("节点执行成功: ID={}, Agent={}", currentNode.getId(), currentNode.getAgentName());
                        
                        // 将所有后继节点加入就绪队列
                        for (Long successorId : successors.get(currentNodeId)) {
                            if (!executed.contains(successorId)) {
                                readyQueue.offer(successorId);
                            }
                        }
                        
                        // 查找以当前节点为前驱的其他节点（处理多个节点指向同一个后继的情况）
                        for (Node node : allNodes) {
                            if (node.getPrevNodeId() != null && node.getPrevNodeId().equals(currentNodeId) 
                                && !executed.contains(node.getId())) {
                                readyQueue.offer(node.getId());
                            }
                        }
                        
                    } catch (Exception e) {
                        // 执行失败，更新状态为 FAILED
                        currentNode.setStatus(Node.NodeStatus.FAILED);
                        nodeMapper.updateById(currentNode);
                        
                        log.error("节点执行失败: ID=" + currentNode.getId() + ", Agent=" + currentNode.getAgentName(), e);
                        throw new RuntimeException("节点执行失败: " + currentNode.getId(), e);
                    }
                } else {
                    log.debug("跳过非 PENDING 状态的节点: ID={}, Status={}", 
                        currentNode.getId(), currentNode.getStatus());
                    executed.add(currentNodeId);
                    
                    // 即使跳过，也要将后继节点加入队列
                    for (Long successorId : successors.get(currentNodeId)) {
                        if (!executed.contains(successorId)) {
                            readyQueue.offer(successorId);
                        }
                    }
                }
                
                // 如果到达结束节点，可以提前结束
                if ("end".equals(currentNode.getAgentName())) {
                    log.debug("到达结束节点，DAG 执行完成");
                    break;
                }
            }
            
            log.info("DAG 节点执行完成，共执行了 {} 个节点", executedCount);
            
        } catch (Exception e) {
            log.error("DAG 节点执行过程中发生错误", e);
            throw new RuntimeException("DAG 节点执行失败", e);
        }
    }
    
    /**
     * 执行单个节点 - 使用策略模式调用对应的 Agent
     */
    private void executeNode(Node node) {
        String agentName = node.getAgentName();
        
        log.debug("执行节点，Agent: {}", agentName);
        
        switch (agentName) {
            case "plan_agent":
                planAgentService.run(node);
                break;
            case "geo_agent":
                geoAgentService.run(node);
                break;
            case "web_search_agent":
                webSearchAgentService.run(node);
                break;
            case "start":
            case "end":
                // 开始和结束节点不需要执行
                log.debug("跳过执行节点: {}", agentName);
                break;
            default:
                log.warn("未知的 Agent 类型: {}", agentName);
                throw new RuntimeException("未知的 Agent 类型: " + agentName);
        }
    }





}
