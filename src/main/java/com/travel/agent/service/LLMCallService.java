package com.travel.agent.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.travel.agent.service.tool.FunctionCallTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.http.*;

import java.util.List;
import java.util.Map;

/**
 * LLM调用服务，支持千问大模型
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Service
public class LLMCallService {

    private static final Logger log = LoggerFactory.getLogger(LLMCallService.class);

    @Value("${dashscope.api.key:}")
    private String dashscopeApiKey;

    @Value("${dashscope.api.url:https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions}")
    private String dashscopeApiUrl;

    private final RestTemplate restTemplate;

    public LLMCallService() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 千问API请求消息
     */
    public static class QwenMessage {
        private String role;
        private String content;

        public QwenMessage() {}

        public QwenMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }

        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }

    /**
     * 千问API请求体
     */
    public static class QwenRequest {
        private String model;
        private List<QwenMessage> messages;

        public QwenRequest() {}

        public QwenRequest(String model, List<QwenMessage> messages) {
            this.model = model;
            this.messages = messages;
        }

        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public List<QwenMessage> getMessages() { return messages; }
        public void setMessages(List<QwenMessage> messages) { this.messages = messages; }
    }

    /**
     * 千问API响应中的选择项
     */
    public static class QwenChoice {
        private int index;
        private QwenMessage message;
        @JsonProperty("finish_reason")
        private String finishReason;

        public int getIndex() { return index; }
        public void setIndex(int index) { this.index = index; }
        public QwenMessage getMessage() { return message; }
        public void setMessage(QwenMessage message) { this.message = message; }
        public String getFinishReason() { return finishReason; }
        public void setFinishReason(String finishReason) { this.finishReason = finishReason; }
    }

    /**
     * 千问API响应中的使用统计
     */
    public static class QwenUsage {
        @JsonProperty("prompt_tokens")
        private int promptTokens;
        @JsonProperty("completion_tokens")
        private int completionTokens;
        @JsonProperty("total_tokens")
        private int totalTokens;

        public int getPromptTokens() { return promptTokens; }
        public void setPromptTokens(int promptTokens) { this.promptTokens = promptTokens; }
        public int getCompletionTokens() { return completionTokens; }
        public void setCompletionTokens(int completionTokens) { this.completionTokens = completionTokens; }
        public int getTotalTokens() { return totalTokens; }
        public void setTotalTokens(int totalTokens) { this.totalTokens = totalTokens; }
    }

    /**
     * 千问API响应体
     */
    public static class QwenResponse {
        private String id;
        private String object;
        private long created;
        private String model;
        private List<QwenChoice> choices;
        private QwenUsage usage;

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getObject() { return object; }
        public void setObject(String object) { this.object = object; }
        public long getCreated() { return created; }
        public void setCreated(long created) { this.created = created; }
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public List<QwenChoice> getChoices() { return choices; }
        public void setChoices(List<QwenChoice> choices) { this.choices = choices; }
        public QwenUsage getUsage() { return usage; }
        public void setUsage(QwenUsage usage) { this.usage = usage; }
    }

    /**
     * Function Call 信息
     */
    public static class FunctionCall {
        private String name;
        private String arguments;

        public FunctionCall() {}

        public FunctionCall(String name, String arguments) {
            this.name = name;
            this.arguments = arguments;
        }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getArguments() { return arguments; }
        public void setArguments(String arguments) { this.arguments = arguments; }
    }

    /**
     * Tool Call 信息
     */
    public static class ToolCall {
        private String id;
        private String type;
        private FunctionCall function;

        public ToolCall() {}

        public ToolCall(String id, String type, FunctionCall function) {
            this.id = id;
            this.type = type;
            this.function = function;
        }

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public FunctionCall getFunction() { return function; }
        public void setFunction(FunctionCall function) { this.function = function; }
    }

    /**
     * 千问API消息（支持Tool Call）
     */
    public static class QwenMessageWithTools extends QwenMessage {
        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls;
        @JsonProperty("tool_call_id")
        private String toolCallId;

        public QwenMessageWithTools() {}

        public QwenMessageWithTools(String role, String content) {
            super(role, content);
        }

        public QwenMessageWithTools(String role, String content, List<ToolCall> toolCalls) {
            super(role, content);
            this.toolCalls = toolCalls;
        }

        public List<ToolCall> getToolCalls() { return toolCalls; }
        public void setToolCalls(List<ToolCall> toolCalls) { this.toolCalls = toolCalls; }
        public String getToolCallId() { return toolCallId; }
        public void setToolCallId(String toolCallId) { this.toolCallId = toolCallId; }
    }

    /**
     * 千问API请求体（支持Function Call）
     */
    public static class QwenRequestWithTools {
        private String model;
        private List<QwenMessage> messages;
        private List<FunctionCallTool> tools;
        @JsonProperty("tool_choice")
        private String toolChoice;

        public QwenRequestWithTools() {}

        public QwenRequestWithTools(String model, List<QwenMessage> messages, List<FunctionCallTool> tools) {
            this.model = model;
            this.messages = messages;
            this.tools = tools;
            this.toolChoice = "auto";
        }

        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public List<QwenMessage> getMessages() { return messages; }
        public void setMessages(List<QwenMessage> messages) { this.messages = messages; }
        public List<FunctionCallTool> getTools() { return tools; }
        public void setTools(List<FunctionCallTool> tools) { this.tools = tools; }
        public String getToolChoice() { return toolChoice; }
        public void setToolChoice(String toolChoice) { this.toolChoice = toolChoice; }
    }

    /**
     * 千问API响应中的选择项（支持Tool Call）
     */
    public static class QwenChoiceWithTools {
        private int index;
        private QwenMessageWithTools message;
        @JsonProperty("finish_reason")
        private String finishReason;

        public int getIndex() { return index; }
        public void setIndex(int index) { this.index = index; }
        public QwenMessageWithTools getMessage() { return message; }
        public void setMessage(QwenMessageWithTools message) { this.message = message; }
        public String getFinishReason() { return finishReason; }
        public void setFinishReason(String finishReason) { this.finishReason = finishReason; }
    }

    /**
     * 千问API响应体（支持Tool Call）
     */
    public static class QwenResponseWithTools {
        private String id;
        private String object;
        private long created;
        private String model;
        private List<QwenChoiceWithTools> choices;
        private QwenUsage usage;

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getObject() { return object; }
        public void setObject(String object) { this.object = object; }
        public long getCreated() { return created; }
        public void setCreated(long created) { this.created = created; }
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public List<QwenChoiceWithTools> getChoices() { return choices; }
        public void setChoices(List<QwenChoiceWithTools> choices) { this.choices = choices; }
        public QwenUsage getUsage() { return usage; }
        public void setUsage(QwenUsage usage) { this.usage = usage; }
    }

    /**
     * 调用千问大模型
     * 
     * @param systemPrompt 系统提示词
     * @param userMessage 用户消息
     * @param model 模型名称，默认为qwen-plus
     * @return 模型响应内容
     * @throws RuntimeException 当API调用失败时抛出异常
     */
    @Retryable(
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0)
    )
    public String callQwenModel(String systemPrompt, String userMessage, String model) {
        try {
            log.info("开始调用千问大模型，模型: {}, 用户消息: {}", model, userMessage);
            
            // 验证API Key
            if (dashscopeApiKey == null || dashscopeApiKey.trim().isEmpty()) {
                throw new RuntimeException("DashScope API Key未配置，请在application.yml中设置dashscope.api.key");
            }

            // 构建请求消息
            List<QwenMessage> messages = List.of(
                new QwenMessage("system", systemPrompt != null ? systemPrompt : "You are a helpful assistant."),
                new QwenMessage("user", userMessage)
            );

            // 构建请求体
            QwenRequest request = new QwenRequest(model != null ? model : "qwen3-max", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(dashscopeApiKey);

            // 创建HTTP请求实体
            HttpEntity<QwenRequest> requestEntity = new HttpEntity<>(request, headers);

            // 发送请求
            log.debug("发送请求到千问API: {}", dashscopeApiUrl);
            ResponseEntity<QwenResponse> responseEntity = restTemplate.postForEntity(
                dashscopeApiUrl, 
                requestEntity, 
                QwenResponse.class
            );

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                QwenResponse response = responseEntity.getBody();
                
                if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                    String content = response.getChoices().get(0).getMessage().getContent();
                    log.info("千问API调用成功，响应长度: {} 字符", content.length());
                    log.debug("千问API响应内容: {}", content);
                    
                    // 记录token使用情况
                    if (response.getUsage() != null) {
                        log.info("Token使用情况 - 输入: {}, 输出: {}, 总计: {}", 
                            response.getUsage().getPromptTokens(),
                            response.getUsage().getCompletionTokens(),
                            response.getUsage().getTotalTokens());
                    }
                    
                    return content;
                } else {
                    throw new RuntimeException("千问API响应中没有有效的选择项");
                }
            } else {
                throw new RuntimeException("千问API调用失败，状态码: " + responseEntity.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用千问大模型时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用千问大模型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用千问大模型（支持Function Call）
     * 
     * @param systemPrompt 系统提示词
     * @param userMessage 用户消息
     * @param tools 可用工具列表
     * @param model 模型名称
     * @return 模型响应
     */
    @Retryable(
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0)
    )
    public QwenResponseWithTools callQwenModelWithTools(String systemPrompt, String userMessage, 
                                                       List<FunctionCallTool> tools, String model) {
        try {
            log.info("开始调用千问大模型（支持Function Call），模型: {}, 工具数量: {}", model, tools != null ? tools.size() : 0);
            
            // 验证API Key
            if (dashscopeApiKey == null || dashscopeApiKey.trim().isEmpty()) {
                throw new RuntimeException("DashScope API Key未配置，请在application.yml中设置dashscope.api.key");
            }

            // 构建请求消息
            List<QwenMessage> messages = List.of(
                new QwenMessage("system", systemPrompt != null ? systemPrompt : "You are a helpful assistant."),
                new QwenMessage("user", userMessage)
            );

            // 构建请求体
            QwenRequestWithTools request = new QwenRequestWithTools(
                model != null ? model : "qwen-plus", 
                messages, 
                tools
            );

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(dashscopeApiKey);

            // 创建HTTP请求实体
            HttpEntity<QwenRequestWithTools> requestEntity = new HttpEntity<>(request, headers);

            // 发送请求
            log.debug("发送请求到千问API: {}", dashscopeApiUrl);
            ResponseEntity<QwenResponseWithTools> responseEntity = restTemplate.postForEntity(
                dashscopeApiUrl, 
                requestEntity, 
                QwenResponseWithTools.class
            );

            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                QwenResponseWithTools response = responseEntity.getBody();
                
                if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                    log.info("千问API调用成功");
                    
                    // 记录token使用情况
                    if (response.getUsage() != null) {
                        log.info("Token使用情况 - 输入: {}, 输出: {}, 总计: {}", 
                            response.getUsage().getPromptTokens(),
                            response.getUsage().getCompletionTokens(),
                            response.getUsage().getTotalTokens());
                    }
                    
                    return response;
                } else {
                    throw new RuntimeException("千问API响应中没有有效的选择项");
                }
            } else {
                throw new RuntimeException("千问API调用失败，状态码: " + responseEntity.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用千问大模型时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用千问大模型失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用千问大模型（使用默认模型qwen-plus）
     * 
     * @param systemPrompt 系统提示词
     * @param userMessage 用户消息
     * @return 模型响应内容
     */
    public String callQwenModel(String systemPrompt, String userMessage) {
        return callQwenModel(systemPrompt, userMessage, "qwen-plus");
    }

    /**
     * 调用千问大模型（支持Function Call，使用默认模型）
     * 
     * @param systemPrompt 系统提示词
     * @param userMessage 用户消息
     * @param tools 可用工具列表
     * @return 模型响应
     */
    public QwenResponseWithTools callQwenModelWithTools(String systemPrompt, String userMessage, 
                                                       List<FunctionCallTool> tools) {
        return callQwenModelWithTools(systemPrompt, userMessage, tools, "qwen-plus");
    }

    /**
     * 调用千问大模型（仅用户消息，使用默认系统提示词）
     * 
     * @param userMessage 用户消息
     * @return 模型响应内容
     */
    public String callQwenModel(String userMessage) {
        return callQwenModel("You are a helpful assistant.", userMessage, "qwen-plus");
    }

    /**
     * 测试千问API连接
     * 
     * @return 测试结果
     */
    public String testQwenConnection() {
        try {
            log.info("开始测试千问API连接...");
            String response = callQwenModel("你好，请简单介绍一下你自己。");
            log.info("千问API连接测试成功");
            return "千问API连接测试成功：" + response;
        } catch (Exception e) {
            log.error("千问API连接测试失败: {}", e.getMessage());
            return "千问API连接测试失败：" + e.getMessage();
        }
    }

    /**
     * 千问大模型调用重试失败后的降级处理方法
     * 
     * @param ex 异常信息
     * @param systemPrompt 系统提示词
     * @param userMessage 用户消息
     * @param model 模型名称
     * @return 降级响应内容
     */
    @Recover
    public String recoverCallQwenModel(Exception ex, String systemPrompt, String userMessage, String model) {
        log.error("千问大模型调用重试失败，执行降级处理。异常: {}, 用户消息: {}, 模型: {}", 
                ex.getMessage(), userMessage, model);
        
        // 返回友好的错误信息，而不是直接抛出异常
        String errorMessage = "抱歉，当前AI服务暂时不可用，请稍后再试。";
        
        // 根据不同的异常类型提供更具体的错误信息
        if (ex instanceof ResourceAccessException) {
            errorMessage = "网络连接异常，请检查网络连接后重试。";
        } else if (ex instanceof HttpServerErrorException) {
            errorMessage = "AI服务暂时繁忙，请稍后再试。";
        } else if (ex.getMessage() != null && ex.getMessage().contains("API Key")) {
            errorMessage = "AI服务配置异常，请联系管理员。";
        }
        
        log.info("返回降级响应: {}", errorMessage);
        return errorMessage;
    }

    /**
     * 检查API配置是否正确
     * 
     * @return 配置检查结果
     */
    public boolean isApiConfigured() {
        boolean configured = dashscopeApiKey != null && !dashscopeApiKey.trim().isEmpty();
        log.debug("千问API配置检查结果: {}", configured ? "已配置" : "未配置");
        return configured;
    }
}
