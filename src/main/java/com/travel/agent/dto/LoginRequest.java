package com.travel.agent.dto;

import lombok.Data;

/**
 * 用户登录请求DTO
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Data
public class LoginRequest {

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 密码
     */
    private String password;

    // Getter and Setter methods
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}