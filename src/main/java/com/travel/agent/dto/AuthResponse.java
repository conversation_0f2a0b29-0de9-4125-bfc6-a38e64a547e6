package com.travel.agent.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 认证响应DTO
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {

    /**
     * JWT token
     */
    private String token;

    /**
     * token类型
     */
    private String type = "Bearer";

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 构造函数
     * 
     * @param token JWT token
     * @param userId 用户ID
     * @param email 用户邮箱
     */
    public AuthResponse(String token, Long userId, String email) {
        this.token = token;
        this.userId = userId;
        this.email = email;
    }
}