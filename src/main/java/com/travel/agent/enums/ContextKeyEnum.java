package com.travel.agent.enums;

public enum ContextKeyEnum {

    /**
     * 系统提示词
     */
    SYSTEM_PROMPT("system_prompt", "系统提示词"),
    /**
     * 用户消息
     */
    USER_MESSAGE("user_message", "用户消息"),

    /**
     * 系统错误信息 工程运行过程中出现的错误
     */
    SYSTEM_ERROR("system_error", "系统错误"),

    /**
     * 综合所有上下文信息 最终提交给 LLM的参数消息。
     */
    LLM_CALL_MESSAGE("llm_call_message", "LLM调用消息"),

    /**
     * agent执行的结果 planAgent 输出结构化执行步骤， 其他agent 输出自然语言结果
     */
    RESULT("agent_result", "agent执行的结果");




    private final String code;
    private final String description;

    ContextKeyEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取消息类型
     * @param code 代码
     * @return 消息类型
     */
    public static ContextKeyEnum fromCode(String code) {
        for (ContextKeyEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的消息类型代码: " + code);
    }


}
