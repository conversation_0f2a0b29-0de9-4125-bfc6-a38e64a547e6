package com.travel.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.agent.entity.SystemPrompt;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统提示词Mapper接口
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Mapper
public interface SystemPromptMapper extends BaseMapper<SystemPrompt> {
    
    /**
     * 根据Agent名称查询系统提示词
     * 
     * @param agentName Agent名称
     * @return 系统提示词
     */
    SystemPrompt findByAgentName(String agentName);
}