package com.travel.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.agent.entity.User;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    User findByEmail(String email);
    
    /**
     * 根据邮箱和验证码查询用户
     * 
     * @param email 邮箱
     * @param verifyCode 验证码
     * @return 用户信息
     */
    User findByEmailAndVerifyCode(String email, String verifyCode);
}