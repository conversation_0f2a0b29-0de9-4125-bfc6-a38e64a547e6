package com.travel.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.agent.entity.NodeContext;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 节点上下文Mapper接口
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Mapper
public interface NodeContextMapper extends BaseMapper<NodeContext> {
    
    /**
     * 根据节点ID查询上下文列表
     * 
     * @param nodeId 节点ID
     * @return 上下文列表
     */
    List<NodeContext> findByNodeId(Long nodeId);
    
    /**
     * 根据节点ID和上下文键查询上下文
     * 
     * @param nodeId 节点ID
     * @param contextKey 上下文键
     * @return 节点上下文
     */
    NodeContext findByNodeIdAndContextKey(Long nodeId, String contextKey);
    
    /**
     * 根据节点ID删除所有上下文
     * 
     * @param nodeId 节点ID
     * @return 删除数量
     */
    int deleteByNodeId(Long nodeId);
}