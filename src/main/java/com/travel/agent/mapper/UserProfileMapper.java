package com.travel.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.agent.entity.UserProfile;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户画像Mapper接口
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Mapper
public interface UserProfileMapper extends BaseMapper<UserProfile> {
    
    /**
     * 根据用户ID查询用户画像列表
     * 
     * @param userId 用户ID
     * @return 用户画像列表
     */
    List<UserProfile> findByUserId(Long userId);
    
    /**
     * 根据用户ID和键查询用户画像
     * 
     * @param userId 用户ID
     * @param key 画像属性键
     * @return 用户画像
     */
    UserProfile findByUserIdAndKey(Long userId, String key);
    
    /**
     * 根据用户ID删除所有画像
     * 
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(Long userId);
}