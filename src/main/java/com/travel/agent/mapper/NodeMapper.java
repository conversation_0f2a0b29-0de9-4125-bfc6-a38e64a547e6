package com.travel.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.agent.entity.Node;
import com.travel.agent.entity.Node.NodeStatus;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 节点Mapper接口
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Mapper
public interface NodeMapper extends BaseMapper<Node> {
    
    /**
     * 根据用户ID查询节点列表
     * 
     * @param userId 用户ID
     * @return 节点列表
     */
    List<Node> findByUserId(Long userId);
    
    /**
     * 根据用户ID和状态查询节点列表
     * 
     * @param userId 用户ID
     * @param status 节点状态
     * @return 节点列表
     */
    List<Node> findByUserIdAndStatus(Long userId, NodeStatus status);
    
    /**
     * 根据前驱节点ID查询节点
     * 
     * @param prevNodeId 前驱节点ID
     * @return 节点
     */
    Node findByPrevNodeId(Long prevNodeId);
    
    /**
     * 根据后继节点ID查询节点
     * 
     * @param nextNodeId 后继节点ID
     * @return 节点
     */
    Node findByNextNodeId(Long nextNodeId);
    
    /**
     * 根据任务ID查询节点列表
     * 
     * @param taskId 任务ID
     * @return 节点列表
     */
    List<Node> findByTaskId(Long taskId);
    
    /**
     * 根据任务ID和状态查询节点列表
     * 
     * @param taskId 任务ID
     * @param status 节点状态
     * @return 节点列表
     */
    List<Node> findByTaskIdAndStatus(Long taskId, NodeStatus status);
    
    /**
     * 根据任务ID查询开始节点（没有前驱节点的节点）
     * 
     * @param taskId 任务ID
     * @return 开始节点
     */
    Node findStartNodeByTaskId(Long taskId);
    
    /**
     * 根据用户ID删除所有节点
     * 
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(Long userId);
}