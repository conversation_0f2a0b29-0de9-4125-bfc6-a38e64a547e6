package com.travel.agent.controller;

import com.travel.agent.service.tool.GeoTool;
import com.travel.agent.service.tool.ToolCallResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * GeoTool 测试控制器
 * 用于测试 GeoTool 类的各种功能
 */
@RestController
@RequestMapping("/api/geo-tool")
public class GeoToolTestController {

    @Autowired
    private GeoTool geoTool;

    /**
     * 获取所有可用的工具
     */
    @GetMapping("/tools")
    public ResponseEntity<?> getAvailableTools() {
        try {
            return ResponseEntity.ok(Map.of(
                "success", true,
                "tools", geoTool.getFunctionCallTools(),
                "toolNames", geoTool.getAvailableToolNames()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 获取指定工具的详细信息
     */
    @GetMapping("/tools/{toolName}")
    public ResponseEntity<?> getToolInfo(@PathVariable String toolName) {
        try {
            if (!geoTool.hasToolSchema(toolName)) {
                return ResponseEntity.ok(Map.of(
                    "success", false,
                    "error", "工具不存在: " + toolName
                ));
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "tool", geoTool.getFunctionCallTool(toolName),
                "schema", geoTool.getToolSchema(toolName)
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 测试地理编码功能
     */
    @PostMapping("/test/geocode")
    public ResponseEntity<?> testGeocode(@RequestBody Map<String, Object> request) {
        try {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("address", request.getOrDefault("address", "北京市朝阳区阜通东大街6号"));
            parameters.put("city", request.get("city"));

            ToolCallResult result = geoTool.callTool("geocode", parameters);
            
            return ResponseEntity.ok(Map.of(
                "success", result.isSuccess(),
                "toolName", result.getToolName(),
                "data", result.getData(),
                "error", result.getError(),
                "executionTime", result.getExecutionTime()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 测试逆地理编码功能
     */
    @PostMapping("/test/regeo")
    public ResponseEntity<?> testRegeo(@RequestBody Map<String, Object> request) {
        try {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("location", request.getOrDefault("location", "116.397428,39.90923"));

            ToolCallResult result = geoTool.callTool("regeo", parameters);
            
            return ResponseEntity.ok(Map.of(
                "success", result.isSuccess(),
                "toolName", result.getToolName(),
                "data", result.getData(),
                "error", result.getError(),
                "executionTime", result.getExecutionTime()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 测试 POI 搜索功能
     */
    @PostMapping("/test/poi-search")
    public ResponseEntity<?> testPoiSearch(@RequestBody Map<String, Object> request) {
        try {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("keywords", request.getOrDefault("keywords", "咖啡厅"));
            parameters.put("city", request.getOrDefault("city", "北京"));
            parameters.put("type", request.get("type"));

            ToolCallResult result = geoTool.callTool("poi_search", parameters);
            
            return ResponseEntity.ok(Map.of(
                "success", result.isSuccess(),
                "toolName", result.getToolName(),
                "data", result.getData(),
                "error", result.getError(),
                "executionTime", result.getExecutionTime()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 测试路径规划功能
     */
    @PostMapping("/test/route-planning")
    public ResponseEntity<?> testRoutePlanning(@RequestBody Map<String, Object> request) {
        try {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("origin", request.getOrDefault("origin", "116.397428,39.90923"));
            parameters.put("destination", request.getOrDefault("destination", "116.407428,39.91923"));
            parameters.put("strategy", request.getOrDefault("strategy", "fastest"));

            ToolCallResult result = geoTool.callTool("route_planning", parameters);
            
            return ResponseEntity.ok(Map.of(
                "success", result.isSuccess(),
                "toolName", result.getToolName(),
                "data", result.getData(),
                "error", result.getError(),
                "executionTime", result.getExecutionTime()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 通用工具调用接口
     */
    @PostMapping("/call/{toolName}")
    public ResponseEntity<?> callTool(@PathVariable String toolName, @RequestBody Map<String, Object> parameters) {
        try {
            ToolCallResult result = geoTool.callTool(toolName, parameters);
            
            return ResponseEntity.ok(Map.of(
                "success", result.isSuccess(),
                "toolName", result.getToolName(),
                "data", result.getData(),
                "error", result.getError(),
                "executionTime", result.getExecutionTime()
            ));
        } catch (Exception e) {
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }
}