package com.travel.agent.controller;

import com.travel.agent.service.LLMCallService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * LLM测试控制器，用于测试千问大模型调用功能
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/llm")
public class LLMTestController {

    private static final Logger log = LoggerFactory.getLogger(LLMTestController.class);

    @Autowired
    private LLMCallService llmCallService;







    /**
     * 调用千问大模型
     */
    @PostMapping("/chat")
    public Map<String, Object> chat(@RequestBody ChatRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (request.getMessage() == null || request.getMessage().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "消息内容不能为空");
                return result;
            }
            
            String response = llmCallService.callQwenModel(
                request.getSystemPrompt(), 
                request.getMessage(), 
                request.getModel()
            );
            
            result.put("success", true);
            result.put("response", response);
            
        } catch (Exception e) {
            log.error("调用千问大模型时发生错误", e);
            result.put("success", false);
            result.put("message", "调用失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 聊天请求数据模型
     */
    public static class ChatRequest {
        private String message;
        private String systemPrompt;
        private String model;

        // Getters and Setters
        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getSystemPrompt() {
            return systemPrompt;
        }

        public void setSystemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }
    }
}