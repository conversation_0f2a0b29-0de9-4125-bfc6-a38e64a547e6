package com.travel.agent.util;

import com.travel.agent.entity.User;

/**
 * 用户上下文工具类
 * 使用ThreadLocal存储当前请求的用户信息
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
public class UserContext {

    private static final ThreadLocal<User> userThreadLocal = new ThreadLocal<>();

    /**
     * 设置当前用户信息
     * 
     * @param user 用户信息
     */
    public static void setCurrentUser(User user) {
        userThreadLocal.set(user);
    }

    /**
     * 获取当前用户信息
     * 
     * @return 当前用户信息，如果未设置则返回null
     */
    public static User getCurrentUser() {
        return userThreadLocal.get();
    }

    /**
     * 获取当前用户ID
     * 
     * @return 当前用户ID，如果未设置用户信息则返回null
     */
    public static Long getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getId() : null;
    }

    /**
     * 获取当前用户邮箱
     * 
     * @return 当前用户邮箱，如果未设置用户信息则返回null
     */
    public static String getCurrentUserEmail() {
        User user = getCurrentUser();
        return user != null ? user.getEmail() : null;
    }

    /**
     * 清除当前用户信息
     * 在请求结束时调用，避免内存泄漏
     */
    public static void clear() {
        userThreadLocal.remove();
    }

    /**
     * 检查是否已设置用户信息
     * 
     * @return 如果已设置用户信息则返回true，否则返回false
     */
    public static boolean hasCurrentUser() {
        return getCurrentUser() != null;
    }
}