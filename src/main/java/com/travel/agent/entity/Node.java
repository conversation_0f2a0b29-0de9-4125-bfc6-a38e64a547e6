package com.travel.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程节点实体类
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Data
@TableName("node")
public class Node {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 任务ID， 代表一系列节点同属于一个任务。
     */
    private Long taskId;

    /**
     * 关联用户
     */
    private Long userId;

    /**
     * 前驱节点ID
     */
    private Long prevNodeId;

    /**
     * 后继节点ID
     */
    private Long nextNodeId;

    /**
     * 执行的Agent
     */
    private String agentName;

    /**
     * 节点状态
     */
    private NodeStatus status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 节点状态枚举
     */
    public enum NodeStatus {
        PENDING("pending"),
        RUNNING("running"),
        PAUSED("paused"),
        COMPLETED("completed"),
        FAILED("failed");

        private final String value;

        NodeStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}