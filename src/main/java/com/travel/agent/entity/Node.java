package com.travel.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 流程节点实体类
 *
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Data
@TableName("node")
public class Node {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 任务ID， 代表一系列节点同属于一个任务。
     */
    private Long taskId;

    /**
     * 关联用户
     */
    private Long userId;

    /**
     * 前驱节点ID
     */
    private Long prevNodeId;

    /**
     * 后继节点ID
     */
    private Long nextNodeId;

    /**
     * 执行的Agent
     */
    private String agentName;

    /**
     * 节点状态
     */
    private NodeStatus status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getPrevNodeId() {
        return prevNodeId;
    }

    public void setPrevNodeId(Long prevNodeId) {
        this.prevNodeId = prevNodeId;
    }

    public Long getNextNodeId() {
        return nextNodeId;
    }

    public void setNextNodeId(Long nextNodeId) {
        this.nextNodeId = nextNodeId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public NodeStatus getStatus() {
        return status;
    }

    public void setStatus(NodeStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 节点状态枚举
     */
    public enum NodeStatus {
        PENDING("pending"),
        RUNNING("running"),
        PAUSED("paused"),
        COMPLETED("completed"),
        FAILED("failed");

        private final String value;

        NodeStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}