package com.travel.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统提示词配置实体类
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Data
@TableName("system_prompt")
public class SystemPrompt {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * Agent 名称，如 geo, ticket, hotel
     */
    private String agentName;

    /**
     * 系统提示词内容
     */
    private String prompt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}