package com.travel.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 节点上下文实体类
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Data
@TableName("node_context")
public class NodeContext {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联节点
     */
    private Long nodeId;

    /**
     * 上下文键
     */
    private String contextKey;

    /**
     * 上下文值
     */
    private String contextValue;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}