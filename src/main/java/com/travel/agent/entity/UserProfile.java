package com.travel.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户画像实体类
 * 
 * <AUTHOR> Agent Team
 * @version 1.0.0
 */
@Data
@TableName("user_profile")
public class UserProfile {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 画像属性键（如 travel_style, budget）
     */
    @TableField("`key`")
    private String key;

    /**
     * 画像属性值（JSON或文本）
     */
    @TableField("`value`")
    private String value;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}