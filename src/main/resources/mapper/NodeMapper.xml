<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.travel.agent.mapper.NodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.travel.agent.entity.Node">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="user_id" property="userId" />
        <result column="prev_node_id" property="prevNodeId" />
        <result column="next_node_id" property="nextNodeId" />
        <result column="agent_name" property="agentName" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, user_id, prev_node_id, next_node_id, agent_name, status, created_at, updated_at
    </sql>

    <!-- 根据用户ID查询节点列表 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID和状态查询节点列表 -->
    <select id="findByUserIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node
        WHERE user_id = #{userId} AND status = #{status}
        ORDER BY created_at DESC
    </select>

    <!-- 根据前驱节点ID查询节点 -->
    <select id="findByPrevNodeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node
        WHERE prev_node_id = #{prevNodeId}
    </select>

    <!-- 根据后继节点ID查询节点 -->
    <select id="findByNextNodeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node
        WHERE next_node_id = #{nextNodeId}
    </select>

    <!-- 根据任务ID查询节点列表 -->
    <select id="findByTaskId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node
        WHERE task_id = #{taskId}
        ORDER BY created_at ASC
    </select>

    <!-- 根据任务ID和状态查询节点列表 -->
    <select id="findByTaskIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node
        WHERE task_id = #{taskId} AND status = #{status}
        ORDER BY created_at ASC
    </select>

    <!-- 根据任务ID查询开始节点（没有前驱节点的节点） -->
    <select id="findStartNodeByTaskId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node
        WHERE task_id = #{taskId} AND prev_node_id IS NULL
        LIMIT 1
    </select>

    <!-- 根据用户ID删除所有节点 -->
    <delete id="deleteByUserId">
        DELETE FROM node WHERE user_id = #{userId}
    </delete>

</mapper>