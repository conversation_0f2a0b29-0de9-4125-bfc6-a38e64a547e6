<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.travel.agent.mapper.NodeContextMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.travel.agent.entity.NodeContext">
        <id column="id" property="id" />
        <result column="node_id" property="nodeId" />
        <result column="context_key" property="contextKey" />
        <result column="context_value" property="contextValue" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, node_id, context_key, context_value, created_at
    </sql>

    <!-- 根据节点ID查询上下文列表 -->
    <select id="findByNodeId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node_context
        WHERE node_id = #{nodeId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据节点ID和上下文键查询上下文 -->
    <select id="findByNodeIdAndContextKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM node_context
        WHERE node_id = #{nodeId} AND context_key = #{contextKey}
    </select>

    <!-- 根据节点ID删除所有上下文 -->
    <delete id="deleteByNodeId">
        DELETE FROM node_context WHERE node_id = #{nodeId}
    </delete>

</mapper>