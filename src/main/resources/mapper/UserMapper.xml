<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.travel.agent.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.travel.agent.entity.User">
        <id column="id" property="id" />
        <result column="email" property="email" />
        <result column="password_hash" property="passwordHash" />
        <result column="verify_code" property="verifyCode" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, email, password_hash, verify_code, created_at, updated_at
    </sql>

    <!-- 根据邮箱查询用户 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE email = #{email}
    </select>

    <!-- 根据邮箱和验证码查询用户 -->
    <select id="findByEmailAndVerifyCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE email = #{email} AND verify_code = #{verifyCode}
    </select>

</mapper>