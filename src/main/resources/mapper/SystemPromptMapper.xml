<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.travel.agent.mapper.SystemPromptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.travel.agent.entity.SystemPrompt">
        <id column="id" property="id" />
        <result column="agent_name" property="agentName" />
        <result column="prompt" property="prompt" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, agent_name, prompt, created_at, updated_at
    </sql>

    <!-- 根据Agent名称查询系统提示词 -->
    <select id="findByAgentName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM system_prompt
        WHERE agent_name = #{agentName}
    </select>

</mapper>