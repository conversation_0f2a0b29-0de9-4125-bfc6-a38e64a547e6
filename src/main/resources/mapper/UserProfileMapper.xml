<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.travel.agent.mapper.UserProfileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.travel.agent.entity.UserProfile">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="key" property="key" />
        <result column="value" property="value" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, `key`, `value`, updated_at
    </sql>

    <!-- 根据用户ID查询用户画像列表 -->
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_profile
        WHERE user_id = #{userId}
        ORDER BY updated_at DESC
    </select>

    <!-- 根据用户ID和键查询用户画像 -->
    <select id="findByUserIdAndKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_profile
        WHERE user_id = #{userId} AND `key` = #{key}
    </select>

    <!-- 根据用户ID删除所有画像 -->
    <delete id="deleteByUserId">
        DELETE FROM user_profile WHERE user_id = #{userId}
    </delete>

</mapper>