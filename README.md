# travel-agent

## 介绍
一个练手的旅游规划agent

## 一些技术细节
1.LLM call 使用http请求千问.支持重试&熔断&降级另一千问模型
2.完善的context管理能力， 配置prompt.
3.DAG结构的任务节点管理， 自动持久化节点信息， 支持 Human in the Loop 和 Time Travel。 
4.使用协调者-工作者 多agent 架构， 单个agent 使用 react模式.
5.MCP工具支持自动发现注入。 工具表配置管理。 （小红书/web search/ 高德 ， 票务酒店查询， 邮件发送 等）
6.记忆模块。 可以增强上下文。 运行中会自动分类添加相关信息（必须是相关的信息才添加） 
7.页面支持流式输出， 支持人工暂停与恢复, 回溯.


## 时序流程

参与角色
•	用户：提出出行需求，确认或调整规划。
•	Web 端：承载交互界面，展示节点信息与执行结果。
•	服务端：全局 orchestrator，协调各服务调用。
•	节点服务：负责 DAG 节点的生成、调度与执行管理。
•	上下文服务：负责构建 LLM 调用所需上下文（规划态 / 执行态）。
•	规划 Agent 服务：调用 LLM，将用户需求拆解为执行步骤。
•	执行 Agent 服务：执行具体任务（调用 LLM / 工具 / API）。
•	记忆服务：管理用户短期/长期记忆，提供用户画像。

⸻

流程步骤
1.	用户请求
•	用户在 Web 端输入出行规划需求，Web 端将请求发送至服务端。
2.	获取用户画像
•	服务端根据用户 ID 从记忆服务中获取用户画像，包括短期与长期记忆信息。
3.	创建流程元数据
•	节点服务为当前任务创建流程实例（flow_id），初始化状态机（未开始 → 规划中 → 执行中 → 已完成）。
•	不再单独创建“开始/结束节点”，而是用流程元数据标记流程边界。
4.	上下文构建（规划态）
•	上下文服务综合用户画像、用户输入、短期/长期记忆与系统消息，拼接成 LLM 调用消息。
•	规划态上下文拼接采用固定模式，确保一致性。
5.	规划 Agent 调用
•	节点服务调用规划 Agent，规划 Agent 调用 LLM，生成任务步骤（结构化格式）。
6.	生成 DAG
•	节点服务解析规划结果，生成 DAG 节点及依赖关系，标记待执行状态。
7.	返回规划结果
•	节点服务将生成的 DAG 节点信息返回给 Web 端，用户可预览并编辑补充信息。
8.	用户确认执行
•	用户在 Web 端确认流程是否执行，或修改/补充节点信息后再次提交至服务端。
9.	执行 DAG
•	服务端通知节点服务开始执行 DAG。
•	节点服务根据 DAG 依赖顺序（可顺序或并发）调度节点执行，并调用对应执行 Agent。
10.	执行 Agent 调用
•	执行 Agent 执行具体任务：
•	如果调用 LLM，则先通过上下文服务构建执行态上下文，再向 LLM 请求。
•	如果调用外部工具，则执行工具 API。
•	执行 Agent 仅返回执行结果，不直接落库。
11.	结果落库
•	节点服务统一将执行结果写入数据库，存为节点上下文信息，关联对应节点。
12.	结果反馈（可配置）
•	节点服务将节点执行结果返回给 Web 端。
•	支持两种模式：
•	流式模式：每个节点完成后推送状态更新。
•	汇总模式：只返回关键节点和最终结果，避免频繁刷新。
13.	流程完成
•	当所有节点执行完成，节点服务更新流程状态为「已完成」，并将最终结果返回给 Web 端。
14.	节点回溯与重执行
•	用户可选择某个节点重新编辑/补充信息。
•	节点服务会从该节点向后重新调度 DAG，之前的结果保持不变。
•	对于非确定性节点（如实时天气/交通），回溯执行时会提示用户“结果可能不同”。
15.	记忆服务更新
•	节点服务将本次任务摘要写入记忆服务：
•	用户提问与补充信息；
•	规划 Agent 生成的任务目标摘要；
•	关键节点的执行摘要与最终结果。
•	仅保存最近 3 个任务为短期记忆，长期记忆由策略控制。


```mermaid
sequenceDiagram
    autonumber
    participant User as 用户
    participant Web as Web端
    participant Server as 服务端
    participant Node as 节点服务
    participant Ctx as 上下文服务
    participant PlanAgent as 规划Agent服务
    participant ExecAgent as 执行Agent服务
    participant Memory as 记忆服务

    %% 1. 用户请求
    User->>Web: 输入出行规划需求
    Web->>Server: 提交用户请求

    %% 2. 获取用户画像
    Server->>Memory: 请求用户画像 (短期/长期记忆)
    Memory-->>Server: 返回用户画像

    %% 3. 创建流程元数据
    Server->>Node: 通知创建流程实例(flow_id)
    Node-->>Server: 返回流程实例已创建

    %% 4. 上下文构建 (规划态)
    Node->>Ctx: 构建规划态上下文 (用户画像+输入+记忆+系统消息)
    Ctx-->>Node: 返回规划态上下文

    %% 5. 规划Agent调用
    Node->>PlanAgent: 调用规划Agent (输入上下文)
    PlanAgent->>PlanAgent: 调用LLM生成任务步骤
    PlanAgent-->>Node: 返回任务步骤 (结构化格式)

    %% 6. 生成DAG
    Node->>Node: 解析任务步骤，生成DAG，标记待执行

    %% 7. 返回规划结果
    Node-->>Web: 返回规划结果 (DAG节点信息)
    Web-->>User: 展示规划结果

    %% 8. 用户确认执行
    User->>Web: 确认或修改规划
    Web->>Server: 提交确认/修改后的流程

    %% 9. 执行DAG
    Server->>Node: 通知执行DAG
    Node->>Node: 按DAG顺序/并发调度节点

    %% 10. 执行Agent调用
    Node->>ExecAgent: 调用执行Agent
    alt 调用LLM
        ExecAgent->>Ctx: 构建执行态上下文
        Ctx-->>ExecAgent: 返回执行态上下文
        ExecAgent->>ExecAgent: 调用LLM
    else 调用外部工具
        ExecAgent->>ExecAgent: 调用工具API
    end
    ExecAgent-->>Node: 返回执行结果

    %% 11. 结果落库
    Node->>Node: 将执行结果写入数据库 (节点上下文信息)

    %% 12. 结果反馈
    Node-->>Web: 返回执行结果 (流式/汇总模式)
    Web-->>User: 展示执行结果

    %% 13. 流程完成
    Node->>Node: 更新流程状态 = 已完成
    Node-->>Server: 返回最终结果
    Server-->>Web: 返回最终结果
    Web-->>User: 展示最终结果

    %% 14. 节点回溯与重执行
    User->>Web: 选择回溯/补充节点信息
    Web->>Server: 提交回溯请求
    Server->>Node: 从该节点重新调度DAG
    Node-->>User: 提示非确定性节点可能结果不同

    %% 15. 记忆服务更新
    Node->>Memory: 存储任务摘要/关键节点结果
    Memory-->>Node: 确认写入完成
```


## 数据库设计

### 用户表
```
CREATE TABLE `user` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    `email` VARCHAR(255) NOT NULL UNIQUE COMMENT '用户邮箱',
    `password_hash` VARCHAR(255) DEFAULT NULL COMMENT '密码哈希（如果支持密码登录）',
    `verify_code` VARCHAR(20) DEFAULT NULL COMMENT '邮箱验证码（临时存储）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 用户画像表

```
CREATE TABLE `user_profile` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
    `key` VARCHAR(100) NOT NULL COMMENT '画像属性键（如 travel_style, budget）',
    `value` TEXT COMMENT '画像属性值（JSON或文本）',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户画像表';
```

### 系统提示词配置表
```
CREATE TABLE `system_prompt` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `agent_name` VARCHAR(100) NOT NULL COMMENT 'Agent 名称，如 geo, ticket, hotel',
    `prompt` TEXT NOT NULL COMMENT '系统提示词内容',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统提示词表';
```

### 节点表
```
CREATE TABLE `node` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL COMMENT '关联用户',
    `task_id` BIGINT NOT NULL COMMENT 'task_id',
    `prev_node_id` BIGINT DEFAULT NULL COMMENT '前驱节点ID',
    `next_node_id` BIGINT DEFAULT NULL COMMENT '后继节点ID',
    `agent_name` VARCHAR(100) NOT NULL COMMENT '执行的Agent',
    `status` ENUM('pending','running','paused','completed','failed') DEFAULT 'pending' COMMENT '节点状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程节点表';
```

### 节点上下文表
```
CREATE TABLE `node_context` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `node_id` BIGINT NOT NULL COMMENT '关联节点',
    `context_key` VARCHAR(100) NOT NULL COMMENT '上下文键',
    `context_value` TEXT COMMENT '上下文值（JSON/文本）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点上下文表';
```
